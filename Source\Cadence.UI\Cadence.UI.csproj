﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.3.4" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.4" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.4" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.4" />
    <!--Condition
    below is needed to remove Avalonia.Diagnostics package from build output in Release
    configuration.-->
    <PackageReference Include="Avalonia.Diagnostics" Version="11.3.4">
      <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
      <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Cadence.Client\Cadence.Client.csproj" />
    <ProjectReference Include="..\Cadence.Rendering\Cadence.Rendering.csproj" />
    <ProjectReference Include="..\Cadence.Infrastructure\Cadence.Infrastructure.csproj" />
  </ItemGroup>
</Project>