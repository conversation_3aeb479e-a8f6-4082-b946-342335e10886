C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.UI.exe
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.UI.deps.json
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.UI.runtimeconfig.json
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.UI.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.UI.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Base.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Controls.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.DesignerSupport.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Dialogs.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Markup.Xaml.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Markup.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Metal.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.MicroCom.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.OpenGL.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Vulkan.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Controls.ColorPicker.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Desktop.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Diagnostics.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Fonts.Inter.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.FreeDesktop.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Native.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Remote.Protocol.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Skia.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Themes.Fluent.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Themes.Simple.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Win32.Automation.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.Win32.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Avalonia.X11.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\HarfBuzzSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\MicroCom.Runtime.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Microsoft.DotNet.PlatformAbstractions.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Microsoft.Extensions.DependencyModel.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Silk.NET.Core.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Silk.NET.WebGPU.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\SkiaSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Tmds.DBus.Protocol.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\av_libglesv2.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\libHarfBuzzSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\wgpu_native.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\libSkiaSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Application.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Client.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Domain.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Infrastructure.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Rendering.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Client.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Rendering.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Infrastructure.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Application.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\win-x64\Cadence.Domain.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.csproj.AssemblyReference.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Avalonia\Resources.Inputs.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Avalonia\resources
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.GeneratedMSBuildEditorConfig.editorconfig
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.AssemblyInfoInputs.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.AssemblyInfo.cs
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.csproj.CoreCompileInputs.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.csproj.Up2Date
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\refint\Cadence.UI.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\Cadence.UI.genruntimeconfig.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\win-x64\ref\Cadence.UI.dll
