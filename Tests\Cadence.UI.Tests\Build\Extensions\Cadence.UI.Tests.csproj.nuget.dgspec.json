{"format": 1, "restore": {"C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.UI.Tests\\Cadence.UI.Tests.csproj": {}}, "projects": {"C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj", "projectName": "Cadence.Application", "projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Cadence.Domain.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Cadence.Domain.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}, "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Client\\Cadence.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Client\\Cadence.Client.csproj", "projectName": "Cadence.Client", "projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Client\\Cadence.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Client\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}, "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Cadence.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Cadence.Domain.csproj", "projectName": "Cadence.Domain", "projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Cadence.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}, "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Infrastructure\\Cadence.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Infrastructure\\Cadence.Infrastructure.csproj", "projectName": "Cadence.Infrastructure", "projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Infrastructure\\Cadence.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Infrastructure\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}, "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Rendering\\Cadence.Rendering.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Rendering\\Cadence.Rendering.csproj", "projectName": "Cadence.Rendering", "projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Rendering\\Cadence.Rendering.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Rendering\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Silk.NET.WebGPU": {"target": "Package", "version": "[2.22.0, )"}, "Silk.NET.WebGPU.Native.WGPU": {"target": "Package", "version": "[2.22.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}, "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.UI\\Cadence.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.UI\\Cadence.UI.csproj", "projectName": "Cadence.UI", "projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.UI\\Cadence.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.UI\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Client\\Cadence.Client.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Client\\Cadence.Client.csproj"}, "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Infrastructure\\Cadence.Infrastructure.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Infrastructure\\Cadence.Infrastructure.csproj"}, "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Rendering\\Cadence.Rendering.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Rendering\\Cadence.Rendering.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.3.4, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.3.4, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.3.4, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.3.4, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.3.4, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}, "runtimes": {"win-x64": {"#import": []}}}, "C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.UI.Tests\\Cadence.UI.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.UI.Tests\\Cadence.UI.Tests.csproj", "projectName": "Cadence.UI.Tests", "projectPath": "C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.UI.Tests\\Cadence.UI.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.UI.Tests\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Dev\\Projects\\Cadence\\Source\\Cadence.UI\\Cadence.UI.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.UI\\Cadence.UI.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.14.0, )"}, "NSubstitute": {"target": "Package", "version": "[5.1.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.4, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}}}