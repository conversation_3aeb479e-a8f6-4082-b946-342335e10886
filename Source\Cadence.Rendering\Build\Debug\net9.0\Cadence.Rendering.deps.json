{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Cadence.Rendering/1.0.0": {"dependencies": {"Silk.NET.WebGPU": "2.22.0", "Silk.NET.WebGPU.Native.WGPU": "2.22.0"}, "runtime": {"Cadence.Rendering.dll": {}}}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"runtime": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "3.1.6.0", "fileVersion": "3.100.620.31604"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Silk.NET.Core/2.22.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "3.1.6", "Microsoft.Extensions.DependencyModel": "8.0.0"}, "runtime": {"lib/net6.0/Silk.NET.Core.dll": {"assemblyVersion": "2.22.0.0", "fileVersion": "2.22.0.0"}}}, "Silk.NET.WebGPU/2.22.0": {"dependencies": {"Silk.NET.Core": "2.22.0"}, "runtime": {"lib/net5.0/Silk.NET.WebGPU.dll": {"assemblyVersion": "2.22.0.0", "fileVersion": "2.22.0.0"}}}, "Silk.NET.WebGPU.Native.WGPU/2.22.0": {"runtimeTargets": {"runtimes/linux-arm/native/libwgpu_native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libwgpu_native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libwgpu_native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libwgpu_native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libwgpu_native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/wgpu_native.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/wgpu_native.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/wgpu_native.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"Cadence.Rendering/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-jek4XYaQ/PGUwDKKhwR8K47Uh1189PFzMeLqO83mXrXQVIpARZCcfuDedH50YDTepBkfijCZN5U/vZi++erxtg==", "path": "microsoft.dotnet.platformabstractions/3.1.6", "hashPath": "microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Silk.NET.Core/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-XbDilPmvsQIrvxZkeVCNNX3LyRtA3hN6UykAiyZjlu/JkYh68U3WQummpP+j7jsxlX8s/pOrrU3vbN8LnMl0VA==", "path": "silk.net.core/2.22.0", "hashPath": "silk.net.core.2.22.0.nupkg.sha512"}, "Silk.NET.WebGPU/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-exCUhXxBWFlW8pV6krbf5K1ZwbhDtBM2qZa1HJa76CZ4RaMml5crmb1m+9qYERPhiQIEvK0EulCMs6CJ5p8bKQ==", "path": "silk.net.webgpu/2.22.0", "hashPath": "silk.net.webgpu.2.22.0.nupkg.sha512"}, "Silk.NET.WebGPU.Native.WGPU/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-b8D5q0HMYL85+vXzudS7VzTwANzOWJqA14EgMIIGd0YqZTbH8tOvk8ND1WZ2G4pEMtbvF8kYpf8kzTzo3g1Qqw==", "path": "silk.net.webgpu.native.wgpu/2.22.0", "hashPath": "silk.net.webgpu.native.wgpu.2.22.0.nupkg.sha512"}}}