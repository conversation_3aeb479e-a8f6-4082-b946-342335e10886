is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Cadence.UI
build_property.ProjectDir = C:\Dev\Projects\Cadence\Source\Cadence.UI\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Dev/Projects/Cadence/Source/Cadence.UI/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[C:/Dev/Projects/Cadence/Source/Cadence.UI/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[C:/Dev/Projects/Cadence/Source/Cadence.UI/Views/DateShelfView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[C:/Dev/Projects/Cadence/Source/Cadence.UI/Views/HeaderBarView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[C:/Dev/Projects/Cadence/Source/Cadence.UI/Views/SidePanelView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[C:/Dev/Projects/Cadence/Source/Cadence.UI/Views/TimelineView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
