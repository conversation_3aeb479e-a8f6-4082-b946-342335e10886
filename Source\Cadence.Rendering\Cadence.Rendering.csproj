﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="Pipelines\Shaders\**\*.wgsl" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Silk.NET.WebGPU" Version="2.22.0" />
    <PackageReference Include="Silk.NET.WebGPU.Native.WGPU" Version="2.22.0" />
  </ItemGroup>


</Project>