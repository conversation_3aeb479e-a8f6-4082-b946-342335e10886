{"version": 3, "targets": {"net9.0": {"Castle.Core/5.1.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}}, "coverlet.collector/6.0.4": {"type": "package", "build": {"build/netstandard2.0/coverlet.collector.targets": {}}}, "FluentAssertions/6.12.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "4.4.0"}, "compile": {"lib/net6.0/FluentAssertions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/FluentAssertions.dll": {"related": ".pdb;.xml"}}}, "Microsoft.CodeAnalysis.NetAnalyzers/9.0.0": {"type": "package", "build": {"buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.props": {}, "buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.targets": {}}}, "Microsoft.CodeCoverage/17.14.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "build": {"build/netstandard2.0/Microsoft.CodeCoverage.props": {}, "build/netstandard2.0/Microsoft.CodeCoverage.targets": {}}}, "Microsoft.NET.Test.Sdk/17.14.0": {"type": "package", "dependencies": {"Microsoft.CodeCoverage": "17.14.0", "Microsoft.TestPlatform.TestHost": "17.14.0"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "build": {"build/net8.0/Microsoft.NET.Test.Sdk.props": {}, "build/net8.0/Microsoft.NET.Test.Sdk.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.NET.Test.Sdk.props": {}}}, "Microsoft.TestPlatform.ObjectModel/17.14.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/net8.0/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/net8.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "runtime": {"lib/net8.0/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/net8.0/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/net8.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "resource": {"lib/net8.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.14.0": {"type": "package", "dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.14.0", "Newtonsoft.Json": "13.0.3"}, "compile": {"lib/net8.0/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/net8.0/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/net8.0/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/net8.0/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/net8.0/Microsoft.TestPlatform.Utilities.dll": {}, "lib/net8.0/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/net8.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/net8.0/testhost.dll": {"related": ".deps.json"}}, "runtime": {"lib/net8.0/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/net8.0/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/net8.0/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/net8.0/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/net8.0/Microsoft.TestPlatform.Utilities.dll": {}, "lib/net8.0/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/net8.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/net8.0/testhost.dll": {"related": ".deps.json"}}, "resource": {"lib/net8.0/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/net8.0/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/net8.0/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/net8.0/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/net8.0/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/net8.0/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/net8.0/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/net8.0/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/net8.0/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/net8.0/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/net8.0/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/net8.0/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"build/net8.0/Microsoft.TestPlatform.TestHost.props": {}, "build/net8.0/Microsoft.TestPlatform.TestHost.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NSubstitute/5.1.0": {"type": "package", "dependencies": {"Castle.Core": "5.1.1"}, "compile": {"lib/net6.0/NSubstitute.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/NSubstitute.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/4.4.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "4.4.0"}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "xunit/2.9.2": {"type": "package", "dependencies": {"xunit.analyzers": "1.16.0", "xunit.assert": "2.9.2", "xunit.core": "[2.9.2]"}}, "xunit.abstractions/2.0.3": {"type": "package", "compile": {"lib/netstandard2.0/xunit.abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/xunit.abstractions.dll": {"related": ".xml"}}}, "xunit.analyzers/1.16.0": {"type": "package"}, "xunit.assert/2.9.2": {"type": "package", "compile": {"lib/net6.0/xunit.assert.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/xunit.assert.dll": {"related": ".xml"}}}, "xunit.core/2.9.2": {"type": "package", "dependencies": {"xunit.extensibility.core": "[2.9.2]", "xunit.extensibility.execution": "[2.9.2]"}, "build": {"build/xunit.core.props": {}, "build/xunit.core.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/xunit.core.props": {}, "buildMultiTargeting/xunit.core.targets": {}}}, "xunit.extensibility.core/2.9.2": {"type": "package", "dependencies": {"xunit.abstractions": "2.0.3"}, "compile": {"lib/netstandard1.1/xunit.core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.1/xunit.core.dll": {"related": ".xml"}}}, "xunit.extensibility.execution/2.9.2": {"type": "package", "dependencies": {"xunit.extensibility.core": "[2.9.2]"}, "compile": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"related": ".xml"}}}, "xunit.runner.visualstudio/2.8.2": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "build": {"build/net6.0/xunit.runner.visualstudio.props": {}}}, "Cadence.Application/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Cadence.Domain": "1.0.0"}, "compile": {"bin/placeholder/Cadence.Application.dll": {}}, "runtime": {"bin/placeholder/Cadence.Application.dll": {}}}, "Cadence.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/Cadence.Domain.dll": {}}, "runtime": {"bin/placeholder/Cadence.Domain.dll": {}}}}}, "libraries": {"Castle.Core/5.1.1": {"sha512": "rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "type": "package", "path": "castle.core/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle-logo.png", "castle.core.5.1.1.nupkg.sha512", "castle.core.nuspec", "lib/net462/Castle.Core.dll", "lib/net462/Castle.Core.xml", "lib/net6.0/Castle.Core.dll", "lib/net6.0/Castle.Core.xml", "lib/netstandard2.0/Castle.Core.dll", "lib/netstandard2.0/Castle.Core.xml", "lib/netstandard2.1/Castle.Core.dll", "lib/netstandard2.1/Castle.Core.xml", "readme.txt"]}, "coverlet.collector/6.0.4": {"sha512": "lkhqpF8Pu2Y7IiN7OntbsTtdbpR1syMsm2F3IgX6ootA4ffRqWL5jF7XipHuZQTdVuWG/gVAAcf8mjk8Tz0xPg==", "type": "package", "path": "coverlet.collector/6.0.4", "files": [".nupkg.metadata", ".signature.p7s", "VSTestIntegration.md", "build/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "build/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "build/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "build/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "build/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "build/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll", "build/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "build/netstandard2.0/Mono.Cecil.Mdb.dll", "build/netstandard2.0/Mono.Cecil.Pdb.dll", "build/netstandard2.0/Mono.Cecil.Rocks.dll", "build/netstandard2.0/Mono.Cecil.dll", "build/netstandard2.0/Newtonsoft.Json.dll", "build/netstandard2.0/NuGet.Frameworks.dll", "build/netstandard2.0/NuGet.Versioning.dll", "build/netstandard2.0/System.Buffers.dll", "build/netstandard2.0/System.Collections.Immutable.dll", "build/netstandard2.0/System.Memory.dll", "build/netstandard2.0/System.Numerics.Vectors.dll", "build/netstandard2.0/System.Reflection.Metadata.dll", "build/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "build/netstandard2.0/System.Text.Encodings.Web.dll", "build/netstandard2.0/System.Text.Json.dll", "build/netstandard2.0/System.Threading.Tasks.Extensions.dll", "build/netstandard2.0/coverlet.collector.deps.json", "build/netstandard2.0/coverlet.collector.dll", "build/netstandard2.0/coverlet.collector.pdb", "build/netstandard2.0/coverlet.collector.targets", "build/netstandard2.0/coverlet.core.dll", "build/netstandard2.0/coverlet.core.pdb", "build/netstandard2.0/coverlet.core.xml", "build/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "coverlet-icon.png", "coverlet.collector.6.0.4.nupkg.sha512", "coverlet.collector.nuspec"]}, "FluentAssertions/6.12.0": {"sha512": "ZXhHT2YwP9lajrwSKbLlFqsmCCvFJMoRSK9t7sImfnCyd0OB3MhgxdoMcVqxbq1iyxD6mD2fiackWmBb7ayiXQ==", "type": "package", "path": "fluentassertions/6.12.0", "files": [".nupkg.metadata", ".signature.p7s", "FluentAssertions.png", "fluentassertions.6.12.0.nupkg.sha512", "fluentassertions.nuspec", "lib/net47/FluentAssertions.dll", "lib/net47/FluentAssertions.pdb", "lib/net47/FluentAssertions.xml", "lib/net6.0/FluentAssertions.dll", "lib/net6.0/FluentAssertions.pdb", "lib/net6.0/FluentAssertions.xml", "lib/netcoreapp2.1/FluentAssertions.dll", "lib/netcoreapp2.1/FluentAssertions.pdb", "lib/netcoreapp2.1/FluentAssertions.xml", "lib/netcoreapp3.0/FluentAssertions.dll", "lib/netcoreapp3.0/FluentAssertions.pdb", "lib/netcoreapp3.0/FluentAssertions.xml", "lib/netstandard2.0/FluentAssertions.dll", "lib/netstandard2.0/FluentAssertions.pdb", "lib/netstandard2.0/FluentAssertions.xml", "lib/netstandard2.1/FluentAssertions.dll", "lib/netstandard2.1/FluentAssertions.pdb", "lib/netstandard2.1/FluentAssertions.xml"]}, "Microsoft.CodeAnalysis.NetAnalyzers/9.0.0": {"sha512": "JajbvkrBgtdRghavIjcJuNHMOja4lqBmEezbhZyqWPYh2cpLhT5mPpfC7NQVDO4IehWQum9t/nwF4v+qQGtYWg==", "type": "package", "path": "microsoft.codeanalysis.netanalyzers/9.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.NetAnalyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.NetAnalyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.NetAnalyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.NetAnalyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "buildTransitive/DisableNETAnalyzersForNuGetPackage.props", "buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.props", "buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.targets", "buildTransitive/config/analysislevel_10_all.globalconfig", "buildTransitive/config/analysislevel_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_default.globalconfig", "buildTransitive/config/analysislevel_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_minimum.globalconfig", "buildTransitive/config/analysislevel_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_none.globalconfig", "buildTransitive/config/analysislevel_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_recommended.globalconfig", "buildTransitive/config/analysislevel_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_all.globalconfig", "buildTransitive/config/analysislevel_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_default.globalconfig", "buildTransitive/config/analysislevel_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_minimum.globalconfig", "buildTransitive/config/analysislevel_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_none.globalconfig", "buildTransitive/config/analysislevel_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_recommended.globalconfig", "buildTransitive/config/analysislevel_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_all.globalconfig", "buildTransitive/config/analysislevel_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_default.globalconfig", "buildTransitive/config/analysislevel_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_minimum.globalconfig", "buildTransitive/config/analysislevel_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_none.globalconfig", "buildTransitive/config/analysislevel_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_recommended.globalconfig", "buildTransitive/config/analysislevel_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_all.globalconfig", "buildTransitive/config/analysislevel_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_default.globalconfig", "buildTransitive/config/analysislevel_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_minimum.globalconfig", "buildTransitive/config/analysislevel_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_none.globalconfig", "buildTransitive/config/analysislevel_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_recommended.globalconfig", "buildTransitive/config/analysislevel_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_all.globalconfig", "buildTransitive/config/analysislevel_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_default.globalconfig", "buildTransitive/config/analysislevel_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_minimum.globalconfig", "buildTransitive/config/analysislevel_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_none.globalconfig", "buildTransitive/config/analysislevel_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_recommended.globalconfig", "buildTransitive/config/analysislevel_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_all.globalconfig", "buildTransitive/config/analysislevel_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_default.globalconfig", "buildTransitive/config/analysislevel_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_minimum.globalconfig", "buildTransitive/config/analysislevel_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_none.globalconfig", "buildTransitive/config/analysislevel_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_recommended.globalconfig", "buildTransitive/config/analysislevel_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_all.globalconfig", "buildTransitive/config/analysisleveldesign_10_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_default.globalconfig", "buildTransitive/config/analysisleveldesign_10_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_none.globalconfig", "buildTransitive/config/analysisleveldesign_10_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_all.globalconfig", "buildTransitive/config/analysisleveldesign_5_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_default.globalconfig", "buildTransitive/config/analysisleveldesign_5_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_none.globalconfig", "buildTransitive/config/analysisleveldesign_5_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_all.globalconfig", "buildTransitive/config/analysisleveldesign_6_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_default.globalconfig", "buildTransitive/config/analysisleveldesign_6_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_none.globalconfig", "buildTransitive/config/analysisleveldesign_6_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_all.globalconfig", "buildTransitive/config/analysisleveldesign_7_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_default.globalconfig", "buildTransitive/config/analysisleveldesign_7_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_none.globalconfig", "buildTransitive/config/analysisleveldesign_7_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_all.globalconfig", "buildTransitive/config/analysisleveldesign_8_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_default.globalconfig", "buildTransitive/config/analysisleveldesign_8_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_none.globalconfig", "buildTransitive/config/analysisleveldesign_8_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_all.globalconfig", "buildTransitive/config/analysisleveldesign_9_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_default.globalconfig", "buildTransitive/config/analysisleveldesign_9_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_none.globalconfig", "buildTransitive/config/analysisleveldesign_9_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_all.globalconfig", "buildTransitive/config/analysislevelglobalization_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_default.globalconfig", "buildTransitive/config/analysislevelglobalization_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_none.globalconfig", "buildTransitive/config/analysislevelglobalization_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_all.globalconfig", "buildTransitive/config/analysislevelglobalization_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_default.globalconfig", "buildTransitive/config/analysislevelglobalization_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_none.globalconfig", "buildTransitive/config/analysislevelglobalization_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_all.globalconfig", "buildTransitive/config/analysislevelglobalization_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_default.globalconfig", "buildTransitive/config/analysislevelglobalization_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_none.globalconfig", "buildTransitive/config/analysislevelglobalization_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_all.globalconfig", "buildTransitive/config/analysislevelglobalization_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_default.globalconfig", "buildTransitive/config/analysislevelglobalization_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_none.globalconfig", "buildTransitive/config/analysislevelglobalization_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_all.globalconfig", "buildTransitive/config/analysislevelglobalization_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_default.globalconfig", "buildTransitive/config/analysislevelglobalization_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_none.globalconfig", "buildTransitive/config/analysislevelglobalization_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_all.globalconfig", "buildTransitive/config/analysislevelglobalization_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_default.globalconfig", "buildTransitive/config/analysislevelglobalization_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_none.globalconfig", "buildTransitive/config/analysislevelglobalization_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_all.globalconfig", "buildTransitive/config/analysislevelnaming_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_default.globalconfig", "buildTransitive/config/analysislevelnaming_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_none.globalconfig", "buildTransitive/config/analysislevelnaming_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_all.globalconfig", "buildTransitive/config/analysislevelnaming_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_default.globalconfig", "buildTransitive/config/analysislevelnaming_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_none.globalconfig", "buildTransitive/config/analysislevelnaming_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_all.globalconfig", "buildTransitive/config/analysislevelnaming_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_default.globalconfig", "buildTransitive/config/analysislevelnaming_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_none.globalconfig", "buildTransitive/config/analysislevelnaming_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_all.globalconfig", "buildTransitive/config/analysislevelnaming_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_default.globalconfig", "buildTransitive/config/analysislevelnaming_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_none.globalconfig", "buildTransitive/config/analysislevelnaming_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_all.globalconfig", "buildTransitive/config/analysislevelnaming_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_default.globalconfig", "buildTransitive/config/analysislevelnaming_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_none.globalconfig", "buildTransitive/config/analysislevelnaming_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_all.globalconfig", "buildTransitive/config/analysislevelnaming_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_default.globalconfig", "buildTransitive/config/analysislevelnaming_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_none.globalconfig", "buildTransitive/config/analysislevelnaming_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_all.globalconfig", "buildTransitive/config/analysislevelperformance_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_default.globalconfig", "buildTransitive/config/analysislevelperformance_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_none.globalconfig", "buildTransitive/config/analysislevelperformance_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_all.globalconfig", "buildTransitive/config/analysislevelperformance_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_default.globalconfig", "buildTransitive/config/analysislevelperformance_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_none.globalconfig", "buildTransitive/config/analysislevelperformance_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_all.globalconfig", "buildTransitive/config/analysislevelperformance_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_default.globalconfig", "buildTransitive/config/analysislevelperformance_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_none.globalconfig", "buildTransitive/config/analysislevelperformance_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_all.globalconfig", "buildTransitive/config/analysislevelperformance_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_default.globalconfig", "buildTransitive/config/analysislevelperformance_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_none.globalconfig", "buildTransitive/config/analysislevelperformance_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_all.globalconfig", "buildTransitive/config/analysislevelperformance_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_default.globalconfig", "buildTransitive/config/analysislevelperformance_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_none.globalconfig", "buildTransitive/config/analysislevelperformance_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_all.globalconfig", "buildTransitive/config/analysislevelperformance_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_default.globalconfig", "buildTransitive/config/analysislevelperformance_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_none.globalconfig", "buildTransitive/config/analysislevelperformance_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_all.globalconfig", "buildTransitive/config/analysislevelreliability_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_default.globalconfig", "buildTransitive/config/analysislevelreliability_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_none.globalconfig", "buildTransitive/config/analysislevelreliability_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_all.globalconfig", "buildTransitive/config/analysislevelreliability_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_default.globalconfig", "buildTransitive/config/analysislevelreliability_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_none.globalconfig", "buildTransitive/config/analysislevelreliability_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_all.globalconfig", "buildTransitive/config/analysislevelreliability_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_default.globalconfig", "buildTransitive/config/analysislevelreliability_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_none.globalconfig", "buildTransitive/config/analysislevelreliability_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_all.globalconfig", "buildTransitive/config/analysislevelreliability_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_default.globalconfig", "buildTransitive/config/analysislevelreliability_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_none.globalconfig", "buildTransitive/config/analysislevelreliability_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_all.globalconfig", "buildTransitive/config/analysislevelreliability_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_default.globalconfig", "buildTransitive/config/analysislevelreliability_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_none.globalconfig", "buildTransitive/config/analysislevelreliability_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_all.globalconfig", "buildTransitive/config/analysislevelreliability_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_default.globalconfig", "buildTransitive/config/analysislevelreliability_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_none.globalconfig", "buildTransitive/config/analysislevelreliability_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_all.globalconfig", "buildTransitive/config/analysislevelsecurity_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_default.globalconfig", "buildTransitive/config/analysislevelsecurity_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_none.globalconfig", "buildTransitive/config/analysislevelsecurity_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_all.globalconfig", "buildTransitive/config/analysislevelsecurity_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_default.globalconfig", "buildTransitive/config/analysislevelsecurity_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_none.globalconfig", "buildTransitive/config/analysislevelsecurity_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_all.globalconfig", "buildTransitive/config/analysislevelsecurity_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_default.globalconfig", "buildTransitive/config/analysislevelsecurity_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_none.globalconfig", "buildTransitive/config/analysislevelsecurity_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_all.globalconfig", "buildTransitive/config/analysislevelsecurity_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_default.globalconfig", "buildTransitive/config/analysislevelsecurity_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_none.globalconfig", "buildTransitive/config/analysislevelsecurity_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_all.globalconfig", "buildTransitive/config/analysislevelsecurity_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_default.globalconfig", "buildTransitive/config/analysislevelsecurity_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_none.globalconfig", "buildTransitive/config/analysislevelsecurity_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_all.globalconfig", "buildTransitive/config/analysislevelsecurity_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_default.globalconfig", "buildTransitive/config/analysislevelsecurity_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_none.globalconfig", "buildTransitive/config/analysislevelsecurity_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_all.globalconfig", "buildTransitive/config/analysislevelusage_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_default.globalconfig", "buildTransitive/config/analysislevelusage_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_minimum.globalconfig", "buildTransitive/config/analysislevelusage_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_none.globalconfig", "buildTransitive/config/analysislevelusage_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_recommended.globalconfig", "buildTransitive/config/analysislevelusage_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_all.globalconfig", "buildTransitive/config/analysislevelusage_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_default.globalconfig", "buildTransitive/config/analysislevelusage_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_minimum.globalconfig", "buildTransitive/config/analysislevelusage_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_none.globalconfig", "buildTransitive/config/analysislevelusage_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_recommended.globalconfig", "buildTransitive/config/analysislevelusage_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_all.globalconfig", "buildTransitive/config/analysislevelusage_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_default.globalconfig", "buildTransitive/config/analysislevelusage_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_minimum.globalconfig", "buildTransitive/config/analysislevelusage_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_none.globalconfig", "buildTransitive/config/analysislevelusage_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_recommended.globalconfig", "buildTransitive/config/analysislevelusage_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_all.globalconfig", "buildTransitive/config/analysislevelusage_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_default.globalconfig", "buildTransitive/config/analysislevelusage_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_minimum.globalconfig", "buildTransitive/config/analysislevelusage_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_none.globalconfig", "buildTransitive/config/analysislevelusage_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_recommended.globalconfig", "buildTransitive/config/analysislevelusage_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_all.globalconfig", "buildTransitive/config/analysislevelusage_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_default.globalconfig", "buildTransitive/config/analysislevelusage_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_minimum.globalconfig", "buildTransitive/config/analysislevelusage_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_none.globalconfig", "buildTransitive/config/analysislevelusage_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_recommended.globalconfig", "buildTransitive/config/analysislevelusage_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_all.globalconfig", "buildTransitive/config/analysislevelusage_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_default.globalconfig", "buildTransitive/config/analysislevelusage_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_minimum.globalconfig", "buildTransitive/config/analysislevelusage_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_none.globalconfig", "buildTransitive/config/analysislevelusage_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_recommended.globalconfig", "buildTransitive/config/analysislevelusage_9_recommended_warnaserror.globalconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.NetAnalyzers.md", "documentation/Microsoft.CodeAnalysis.NetAnalyzers.sarif", "documentation/readme.md", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/DesignRulesDefault/.editorconfig", "editorconfig/DesignRulesEnabled/.editorconfig", "editorconfig/DocumentationRulesDefault/.editorconfig", "editorconfig/DocumentationRulesEnabled/.editorconfig", "editorconfig/GlobalizationRulesDefault/.editorconfig", "editorconfig/GlobalizationRulesEnabled/.editorconfig", "editorconfig/InteroperabilityRulesDefault/.editorconfig", "editorconfig/InteroperabilityRulesEnabled/.editorconfig", "editorconfig/MaintainabilityRulesDefault/.editorconfig", "editorconfig/MaintainabilityRulesEnabled/.editorconfig", "editorconfig/NamingRulesDefault/.editorconfig", "editorconfig/NamingRulesEnabled/.editorconfig", "editorconfig/PerformanceRulesDefault/.editorconfig", "editorconfig/PerformanceRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "editorconfig/ReliabilityRulesDefault/.editorconfig", "editorconfig/ReliabilityRulesEnabled/.editorconfig", "editorconfig/SecurityRulesDefault/.editorconfig", "editorconfig/SecurityRulesEnabled/.editorconfig", "editorconfig/UsageRulesDefault/.editorconfig", "editorconfig/UsageRulesEnabled/.editorconfig", "microsoft.codeanalysis.netanalyzers.9.0.0.nupkg.sha512", "microsoft.codeanalysis.netanalyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/DesignRulesDefault.ruleset", "rulesets/DesignRulesEnabled.ruleset", "rulesets/DocumentationRulesDefault.ruleset", "rulesets/DocumentationRulesEnabled.ruleset", "rulesets/GlobalizationRulesDefault.ruleset", "rulesets/GlobalizationRulesEnabled.ruleset", "rulesets/InteroperabilityRulesDefault.ruleset", "rulesets/InteroperabilityRulesEnabled.ruleset", "rulesets/MaintainabilityRulesDefault.ruleset", "rulesets/MaintainabilityRulesEnabled.ruleset", "rulesets/NamingRulesDefault.ruleset", "rulesets/NamingRulesEnabled.ruleset", "rulesets/PerformanceRulesDefault.ruleset", "rulesets/PerformanceRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "rulesets/ReliabilityRulesDefault.ruleset", "rulesets/ReliabilityRulesEnabled.ruleset", "rulesets/SecurityRulesDefault.ruleset", "rulesets/SecurityRulesEnabled.ruleset", "rulesets/UsageRulesDefault.ruleset", "rulesets/UsageRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeCoverage/17.14.0": {"sha512": "z2GYXGG6LjGoumT59xSB2dMnqSwQBjkxdDJmSJHwy5nPtZ435GXa6wj5hz/lRrAZ7NyXXxZNXVsiHXzHRru5eA==", "type": "package", "path": "microsoft.codecoverage/17.14.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "build/netstandard2.0/CodeCoverage/CodeCoverage.config", "build/netstandard2.0/CodeCoverage/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/Cov_x86.config", "build/netstandard2.0/CodeCoverage/amd64/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/amd64/Cov_x64.config", "build/netstandard2.0/CodeCoverage/amd64/covrun64.dll", "build/netstandard2.0/CodeCoverage/amd64/msdia140.dll", "build/netstandard2.0/CodeCoverage/arm64/Cov_arm64.config", "build/netstandard2.0/CodeCoverage/arm64/covrunarm64.dll", "build/netstandard2.0/CodeCoverage/arm64/msdia140.dll", "build/netstandard2.0/CodeCoverage/codecoveragemessages.dll", "build/netstandard2.0/CodeCoverage/coreclr/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "build/netstandard2.0/CodeCoverage/covrun32.dll", "build/netstandard2.0/CodeCoverage/msdia140.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Interprocess.dll", "build/netstandard2.0/Microsoft.CodeCoverage.props", "build/netstandard2.0/Microsoft.CodeCoverage.targets", "build/netstandard2.0/Microsoft.DiaSymReader.dll", "build/netstandard2.0/Microsoft.VisualStudio.TraceDataCollector.dll", "build/netstandard2.0/Mono.Cecil.Pdb.dll", "build/netstandard2.0/Mono.Cecil.Rocks.dll", "build/netstandard2.0/Mono.Cecil.dll", "build/netstandard2.0/ThirdPartyNotices.txt", "build/netstandard2.0/alpine/x64/Cov_x64.config", "build/netstandard2.0/alpine/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/alpine/x64/libInstrumentationEngine.so", "build/netstandard2.0/arm64/MicrosoftInstrumentationEngine_arm64.dll", "build/netstandard2.0/cs/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/de/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/es/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/fr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/it/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ja/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ko/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/macos/x64/Cov_x64.config", "build/netstandard2.0/macos/x64/libCoverageInstrumentationMethod.dylib", "build/netstandard2.0/macos/x64/libInstrumentationEngine.dylib", "build/netstandard2.0/pl/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ru/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/tr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ubuntu/x64/Cov_x64.config", "build/netstandard2.0/ubuntu/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/ubuntu/x64/libInstrumentationEngine.so", "build/netstandard2.0/x64/MicrosoftInstrumentationEngine_x64.dll", "build/netstandard2.0/x86/MicrosoftInstrumentationEngine_x86.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "lib/net462/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "lib/net8.0/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "microsoft.codecoverage.17.14.0.nupkg.sha512", "microsoft.codecoverage.nuspec"]}, "Microsoft.NET.Test.Sdk/17.14.0": {"sha512": "rTtdOm6C96q9QgP3mS8nUGPGPh5Xm2HnBYJggNmNrJ3LDmX9lJuUIgnJEfvX6wSQY4swUMiCcIXd3OkjhYCgtw==", "type": "package", "path": "microsoft.net.test.sdk/17.14.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net462/Microsoft.NET.Test.Sdk.props", "build/net462/Microsoft.NET.Test.Sdk.targets", "build/net8.0/Microsoft.NET.Test.Sdk.Program.cs", "build/net8.0/Microsoft.NET.Test.Sdk.Program.fs", "build/net8.0/Microsoft.NET.Test.Sdk.Program.vb", "build/net8.0/Microsoft.NET.Test.Sdk.props", "build/net8.0/Microsoft.NET.Test.Sdk.targets", "buildMultiTargeting/Microsoft.NET.Test.Sdk.props", "lib/net462/_._", "lib/net8.0/_._", "microsoft.net.test.sdk.17.14.0.nupkg.sha512", "microsoft.net.test.sdk.nuspec"]}, "Microsoft.TestPlatform.ObjectModel/17.14.0": {"sha512": "3h7y7f/HuY8jdZa163p/55VmGw/fYJwrI8FOtsp4aEQAJaPgBr5LBS25uOfBwRYI95QDiByfaqSPBcWEvuHEwA==", "type": "package", "path": "microsoft.testplatform.objectmodel/17.14.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net462/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net8.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net8.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net8.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "microsoft.testplatform.objectmodel.17.14.0.nupkg.sha512", "microsoft.testplatform.objectmodel.nuspec"]}, "Microsoft.TestPlatform.TestHost/17.14.0": {"sha512": "8htQBKM92s/NXUI/U0/CKKLlvlDfWIo3/mbnY/GS/2XLkBGNIVQufmUpDIzznaZqUpdzspGSsJcLhVN8aRoMaA==", "type": "package", "path": "microsoft.testplatform.testhost/17.14.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "build/net8.0/Microsoft.TestPlatform.TestHost.props", "build/net8.0/Microsoft.TestPlatform.TestHost.targets", "build/net8.0/x64/testhost.dll", "build/net8.0/x64/testhost.exe", "build/net8.0/x86/testhost.x86.dll", "build/net8.0/x86/testhost.x86.exe", "lib/net462/_._", "lib/net8.0/Microsoft.TestPlatform.CommunicationUtilities.dll", "lib/net8.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net8.0/Microsoft.TestPlatform.CrossPlatEngine.dll", "lib/net8.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net8.0/Microsoft.TestPlatform.Utilities.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.Common.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net8.0/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/testhost.deps.json", "lib/net8.0/testhost.dll", "lib/net8.0/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/x64/msdia140.dll", "lib/net8.0/x86/msdia140.dll", "lib/net8.0/zh-<PERSON>/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/net8.0/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/net8.0/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "microsoft.testplatform.testhost.17.14.0.nupkg.sha512", "microsoft.testplatform.testhost.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NSubstitute/5.1.0": {"sha512": "ZCqOP3Kpp2ea7QcLyjMU4wzE+0wmrMN35PQMsdPOHYc2IrvjmusG9hICOiqiOTPKN0gJon6wyCn6ZuGHdNs9hQ==", "type": "package", "path": "nsubstitute/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/NSubstitute.dll", "lib/net462/NSubstitute.xml", "lib/net6.0/NSubstitute.dll", "lib/net6.0/NSubstitute.xml", "lib/netstandard2.0/NSubstitute.dll", "lib/netstandard2.0/NSubstitute.xml", "nsubstitute.5.1.0.nupkg.sha512", "nsubstitute.nuspec"]}, "System.Configuration.ConfigurationManager/4.4.0": {"sha512": "gWwQv/Ug1qWJmHCmN17nAbxJYmQBM/E94QxKLksvUiiKB1Ld3Sc/eK1lgmbSjDFxkQhVuayI/cGFZhpBSodLrg==", "type": "package", "path": "system.configuration.configurationmanager/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "ref/net461/System.Configuration.ConfigurationManager.dll", "ref/net461/System.Configuration.ConfigurationManager.xml", "ref/netstandard2.0/System.Configuration.ConfigurationManager.dll", "ref/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.4.4.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.EventLog/6.0.0": {"sha512": "lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "type": "package", "path": "system.diagnostics.eventlog/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.EventLog.dll", "lib/net461/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/netcoreapp3.1/System.Diagnostics.EventLog.dll", "lib/netcoreapp3.1/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.6.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/4.4.0": {"sha512": "cJV7ScGW7EhatRsjehfvvYVBvtiSMKgN8bOVI0bQhnF5bU7vnHVIsH49Kva7i7GWaWYvmEzkYVk1TC+gZYBEog==", "type": "package", "path": "system.security.cryptography.protecteddata/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.xml", "ref/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "system.security.cryptography.protecteddata.4.4.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "xunit/2.9.2": {"sha512": "7LhFS2N9Z6Xgg8aE5lY95cneYivRMfRI8v+4PATa4S64D5Z/Plkg0qa8dTRHSiGRgVZ/CL2gEfJDE5AUhOX+2Q==", "type": "package", "path": "xunit/2.9.2", "files": [".nupkg.metadata", ".signature.p7s", "_content/README.md", "_content/logo-128-transparent.png", "xunit.2.9.2.nupkg.sha512", "xunit.nuspec"]}, "xunit.abstractions/2.0.3": {"sha512": "pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg==", "type": "package", "path": "xunit.abstractions/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/xunit.abstractions.dll", "lib/net35/xunit.abstractions.xml", "lib/netstandard1.0/xunit.abstractions.dll", "lib/netstandard1.0/xunit.abstractions.xml", "lib/netstandard2.0/xunit.abstractions.dll", "lib/netstandard2.0/xunit.abstractions.xml", "xunit.abstractions.2.0.3.nupkg.sha512", "xunit.abstractions.nuspec"]}, "xunit.analyzers/1.16.0": {"sha512": "hptYM7vGr46GUIgZt21YHO4rfuBAQS2eINbFo16CV/Dqq+24Tp+P5gDCACu1AbFfW4Sp/WRfDPSK8fmUUb8s0Q==", "type": "package", "path": "xunit.analyzers/1.16.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "_content/README.md", "_content/logo-128-transparent.png", "analyzers/dotnet/cs/xunit.analyzers.dll", "analyzers/dotnet/cs/xunit.analyzers.fixes.dll", "tools/install.ps1", "tools/uninstall.ps1", "xunit.analyzers.1.16.0.nupkg.sha512", "xunit.analyzers.nuspec"]}, "xunit.assert/2.9.2": {"sha512": "QkNBAQG4pa66cholm28AxijBjrmki98/vsEh4Sx5iplzotvPgpiotcxqJQMRC8d7RV7nIT8ozh97957hDnZwsQ==", "type": "package", "path": "xunit.assert/2.9.2", "files": [".nupkg.metadata", ".signature.p7s", "_content/README.md", "_content/logo-128-transparent.png", "lib/net6.0/xunit.assert.dll", "lib/net6.0/xunit.assert.xml", "lib/netstandard1.1/xunit.assert.dll", "lib/netstandard1.1/xunit.assert.xml", "xunit.assert.2.9.2.nupkg.sha512", "xunit.assert.nuspec"]}, "xunit.core/2.9.2": {"sha512": "O6RrNSdmZ0xgEn5kT927PNwog5vxTtKrWMihhhrT0Sg9jQ7iBDciYOwzBgP2krBEk5/GBXI18R1lKvmnxGcb4w==", "type": "package", "path": "xunit.core/2.9.2", "files": [".nupkg.metadata", ".signature.p7s", "_content/README.md", "_content/logo-128-transparent.png", "build/xunit.core.props", "build/xunit.core.targets", "buildMultiTargeting/xunit.core.props", "buildMultiTargeting/xunit.core.targets", "xunit.core.2.9.2.nupkg.sha512", "xunit.core.nuspec"]}, "xunit.extensibility.core/2.9.2": {"sha512": "Ol+KlBJz1x8BrdnhN2DeOuLrr1I/cTwtHCggL9BvYqFuVd/TUSzxNT5O0NxCIXth30bsKxgMfdqLTcORtM52yQ==", "type": "package", "path": "xunit.extensibility.core/2.9.2", "files": [".nupkg.metadata", ".signature.p7s", "_content/README.md", "_content/logo-128-transparent.png", "lib/net452/xunit.core.dll", "lib/net452/xunit.core.dll.tdnet", "lib/net452/xunit.core.xml", "lib/net452/xunit.runner.tdnet.dll", "lib/net452/xunit.runner.utility.net452.dll", "lib/netstandard1.1/xunit.core.dll", "lib/netstandard1.1/xunit.core.xml", "xunit.extensibility.core.2.9.2.nupkg.sha512", "xunit.extensibility.core.nuspec"]}, "xunit.extensibility.execution/2.9.2": {"sha512": "rKMpq4GsIUIJibXuZoZ8lYp5EpROlnYaRpwu9Zr0sRZXE7JqJfEEbCsUriZqB+ByXCLFBJyjkTRULMdC+U566g==", "type": "package", "path": "xunit.extensibility.execution/2.9.2", "files": [".nupkg.metadata", ".signature.p7s", "_content/README.md", "_content/logo-128-transparent.png", "lib/net452/xunit.execution.desktop.dll", "lib/net452/xunit.execution.desktop.xml", "lib/netstandard1.1/xunit.execution.dotnet.dll", "lib/netstandard1.1/xunit.execution.dotnet.xml", "xunit.extensibility.execution.2.9.2.nupkg.sha512", "xunit.extensibility.execution.nuspec"]}, "xunit.runner.visualstudio/2.8.2": {"sha512": "vm1tbfXhFmjFMUmS4M0J0ASXz3/U5XvXBa6DOQUL3fEz4Vt6YPhv+ESCarx6M6D+9kJkJYZKCNvJMas1+nVfmQ==", "type": "package", "path": "xunit.runner.visualstudio/2.8.2", "files": [".nupkg.metadata", ".signature.p7s", "_content/README.md", "_content/logo-128-transparent.png", "build/net462/xunit.abstractions.dll", "build/net462/xunit.runner.reporters.net452.dll", "build/net462/xunit.runner.utility.net452.dll", "build/net462/xunit.runner.visualstudio.props", "build/net462/xunit.runner.visualstudio.testadapter.dll", "build/net6.0/xunit.abstractions.dll", "build/net6.0/xunit.runner.reporters.netcoreapp10.dll", "build/net6.0/xunit.runner.utility.netcoreapp10.dll", "build/net6.0/xunit.runner.visualstudio.props", "build/net6.0/xunit.runner.visualstudio.testadapter.dll", "lib/net462/_._", "lib/net6.0/_._", "xunit.runner.visualstudio.2.8.2.nupkg.sha512", "xunit.runner.visualstudio.nuspec"]}, "Cadence.Application/1.0.0": {"type": "project", "path": "../../Source/Cadence.Application/Cadence.Application.csproj", "msbuildProject": "../../Source/Cadence.Application/Cadence.Application.csproj"}, "Cadence.Domain/1.0.0": {"type": "project", "path": "../../Source/Cadence.Domain/Cadence.Domain.csproj", "msbuildProject": "../../Source/Cadence.Domain/Cadence.Domain.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["Cadence.Application >= 1.0.0", "FluentAssertions >= 6.12.0", "Microsoft.CodeAnalysis.NetAnalyzers >= 9.0.0", "Microsoft.NET.Test.Sdk >= 17.14.0", "NSubstitute >= 5.1.0", "coverlet.collector >= 6.0.4", "xunit >= 2.9.2", "xunit.runner.visualstudio >= 2.8.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.Application.Tests\\Cadence.Application.Tests.csproj", "projectName": "Cadence.Application.Tests", "projectPath": "C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.Application.Tests\\Cadence.Application.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Tests\\Cadence.Application.Tests\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj": {"projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Application\\Cadence.Application.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.14.0, )"}, "NSubstitute": {"target": "Package", "version": "[5.1.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.4, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}}