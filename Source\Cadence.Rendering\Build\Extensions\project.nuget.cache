{"version": 2, "dgSpecHash": "J97TAdTsxjk=", "success": true, "projectFilePath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Rendering\\Cadence.Rendering.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.netanalyzers\\9.0.0\\microsoft.codeanalysis.netanalyzers.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.dotnet.platformabstractions\\3.1.6\\microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\8.0.0\\microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\silk.net.core\\2.22.0\\silk.net.core.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\silk.net.webgpu\\2.22.0\\silk.net.webgpu.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\silk.net.webgpu.native.wgpu\\2.22.0\\silk.net.webgpu.native.wgpu.2.22.0.nupkg.sha512"], "logs": []}