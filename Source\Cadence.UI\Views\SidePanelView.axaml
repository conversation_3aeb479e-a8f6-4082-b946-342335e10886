<UserControl xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:vm="clr-namespace:Cadence.UI.Views.ViewModels"
    x:Class="Cadence.UI.Views.SidePanelView"
    x:DataType="vm:SidePanelViewModel"
    MinWidth="240">
    <Border Background="{Binding Background}"
        BorderBrush="{DynamicResource ThemeBorderLowBrush}"
        BorderThickness="0,0,1,0">
        <StackPanel Spacing="8">
            <StackPanel.Margin>
                <Thickness>12</Thickness>
            </StackPanel.Margin>
            <TextBlock Text="Side Panel" FontWeight="SemiBold" />
        </StackPanel>
    </Border>
</UserControl>