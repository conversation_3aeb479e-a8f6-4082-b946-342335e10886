using System;
using System.Runtime.InteropServices;

using System.Threading;
using Silk.NET.WebGPU;

namespace Cadence.Rendering.Device;

public sealed unsafe class WebGpuContext : IDisposable
{
    private static readonly WebGPU _wgpu = WebGPU.GetApi();
    public Instance* Instance { get; private set; }
    public Adapter* Adapter { get; private set; }
    public Silk.NET.WebGPU.Device* Device { get; private set; }
    public static bool IsRuntimeAvailable()
    {
        // Hardened preflight: ensure a compatible wgpu library is present AND exports core symbols
        return TryLoadWithExport("wgpu") || TryLoadWithExport("wgpu_native");
    }

    private static bool TryLoadWithExport(string name)
    {
        if (NativeLibrary.TryLoad(name, out var handle))
        {
            try
            {
                // Check for expected exports; different builds may use either naming
                if (NativeLibrary.TryGetExport(handle, "wgpuCreateInstance", out _)) return true;
                if (NativeLibrary.TryGetExport(handle, "wgpu_create_instance", out _)) return true;
                return false;
            }
            finally
            {
                NativeLibrary.Free(handle);
            }
        }
        return false;
    }


    public Queue* Queue { get; private set; }

    public void Initialize()
    {
        InstanceDescriptor desc = new InstanceDescriptor();
        Instance = _wgpu.CreateInstance(&desc);
        if (Instance is null) throw new InvalidOperationException("Failed to create WebGPU Instance.");

        RequestAdapterOptions options = new()
        {
            PowerPreference = PowerPreference.HighPerformance
        };

        Adapter* adapterLocal = null;
        using (var ready = new ManualResetEventSlim(false))
        {
            PfnRequestAdapterCallback cb = new((status, adapter, message, _) =>
            {
                if (status != RequestAdapterStatus.Success)
                {
                    adapterLocal = null;
                    ready.Set();
                    return;
                }
                adapterLocal = adapter;
                ready.Set();
            });
            _wgpu.InstanceRequestAdapter(Instance, &options, cb, null);
            ready.Wait();
            // Ensure the delegate isn't collected before the callback returns
            GC.KeepAlive(cb);
        }
        Adapter = adapterLocal;
        if (Adapter is null) throw new InvalidOperationException("No suitable WebGPU adapter.");

        Silk.NET.WebGPU.Device* deviceLocal = null;
        using (var ready = new ManualResetEventSlim(false))
        {
            PfnRequestDeviceCallback cb = new((status, device, message, _) =>
            {
                if (status != RequestDeviceStatus.Success)
                {
                    deviceLocal = null;
                    ready.Set();
                    return;
                }
                deviceLocal = device;
                ready.Set();
            });
            _wgpu.AdapterRequestDevice(Adapter, null, cb, null);
            ready.Wait();
            // Ensure the delegate isn't collected before the callback returns
            GC.KeepAlive(cb);
        }
        Device = deviceLocal;
        if (Device is null) throw new InvalidOperationException("Failed to create WebGPU device.");

        Queue = _wgpu.DeviceGetQueue(Device);
    }

    public unsafe void Dispose()
    {
        if (Device != null)
        {
            _wgpu.DeviceDestroy(Device);
            _wgpu.DeviceRelease(Device);
            Device = null;
        }
        if (Adapter != null)
        {
            _wgpu.AdapterRelease(Adapter);
            Adapter = null;
        }
        if (Instance != null)
        {
            _wgpu.InstanceRelease(Instance);
            Instance = null;
        }
        // Do not dispose the global WebGPU API wrapper here; it's shared.
    }
}


