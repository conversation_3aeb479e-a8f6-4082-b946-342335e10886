{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Cadence.UI/1.0.0": {"dependencies": {"Avalonia": "11.3.4", "Avalonia.Desktop": "11.3.4", "Avalonia.Diagnostics": "11.3.4", "Avalonia.Fonts.Inter": "11.3.4", "Avalonia.Themes.Fluent": "11.3.4", "Cadence.Client": "1.0.0", "Cadence.Infrastructure": "1.0.0", "Cadence.Rendering": "1.0.0"}, "runtime": {"Cadence.UI.dll": {}}}, "Avalonia/11.3.4": {"dependencies": {"Avalonia.Remote.Protocol": "11.3.4", "MicroCom.Runtime": "0.11.0"}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.Controls.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "0.7.0.0", "fileVersion": "0.7.0.0"}, "lib/net8.0/Avalonia.Dialogs.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.Markup.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.Metal.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.MicroCom.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.OpenGL.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.Vulkan.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Angle.Windows.Natives/2.1.25547.20250602": {"runtimeTargets": {"runtimes/win-arm64/native/av_libglesv2.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.1.25606.0"}, "runtimes/win-x64/native/av_libglesv2.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.1.25606.0"}, "runtimes/win-x86/native/av_libglesv2.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.1.25606.0"}}}, "Avalonia.Controls.ColorPicker/11.3.4": {"dependencies": {"Avalonia": "11.3.4", "Avalonia.Remote.Protocol": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Desktop/11.3.4": {"dependencies": {"Avalonia": "11.3.4", "Avalonia.Native": "11.3.4", "Avalonia.Skia": "11.3.4", "Avalonia.Win32": "11.3.4", "Avalonia.X11": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.Desktop.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Diagnostics/11.3.4": {"dependencies": {"Avalonia": "11.3.4", "Avalonia.Controls.ColorPicker": "11.3.4", "Avalonia.Themes.Simple": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Fonts.Inter/11.3.4": {"dependencies": {"Avalonia": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.Fonts.Inter.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.FreeDesktop/11.3.4": {"dependencies": {"Avalonia": "11.3.4", "Tmds.DBus.Protocol": "0.21.2"}, "runtime": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Native/11.3.4": {"dependencies": {"Avalonia": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.Native.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}, "runtimeTargets": {"runtimes/osx/native/libAvaloniaNative.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Avalonia.Remote.Protocol/11.3.4": {"runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Skia/11.3.4": {"dependencies": {"Avalonia": "11.3.4", "HarfBuzzSharp": "*******", "HarfBuzzSharp.NativeAssets.Linux": "*******", "SkiaSharp": "2.88.9", "SkiaSharp.NativeAssets.Linux": "2.88.9"}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Themes.Fluent/11.3.4": {"dependencies": {"Avalonia": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.Themes.Fluent.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Themes.Simple/11.3.4": {"dependencies": {"Avalonia": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.Win32/11.3.4": {"dependencies": {"Avalonia": "11.3.4", "Avalonia.Angle.Windows.Natives": "2.1.25547.20250602"}, "runtime": {"lib/net8.0/Avalonia.Win32.Automation.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}, "lib/net8.0/Avalonia.Win32.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "Avalonia.X11/11.3.4": {"dependencies": {"Avalonia": "11.3.4", "Avalonia.FreeDesktop": "11.3.4", "Avalonia.Skia": "11.3.4"}, "runtime": {"lib/net8.0/Avalonia.X11.dll": {"assemblyVersion": "11.3.4.0", "fileVersion": "11.3.4.0"}}}, "HarfBuzzSharp/*******": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}, "runtime": {"lib/net8.0/HarfBuzzSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "*******"}}}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-loongarch64/native/libHarfBuzzSharp.so": {"rid": "linux-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libHarfBuzzSharp.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-riscv64/native/libHarfBuzzSharp.so": {"rid": "linux-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libHarfBuzzSharp.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "0.11.0.0", "fileVersion": "0.11.0.0"}}}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"runtime": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "3.1.6.0", "fileVersion": "3.100.620.31604"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Silk.NET.Core/2.22.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "3.1.6", "Microsoft.Extensions.DependencyModel": "8.0.0"}, "runtime": {"lib/net6.0/Silk.NET.Core.dll": {"assemblyVersion": "2.22.0.0", "fileVersion": "2.22.0.0"}}}, "Silk.NET.WebGPU/2.22.0": {"dependencies": {"Silk.NET.Core": "2.22.0"}, "runtime": {"lib/net5.0/Silk.NET.WebGPU.dll": {"assemblyVersion": "2.22.0.0", "fileVersion": "2.22.0.0"}}}, "Silk.NET.WebGPU.Native.WGPU/2.22.0": {"runtimeTargets": {"runtimes/linux-arm/native/libwgpu_native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libwgpu_native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libwgpu_native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libwgpu_native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libwgpu_native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/wgpu_native.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/wgpu_native.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/wgpu_native.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp/2.88.9": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.9.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"dependencies": {"SkiaSharp": "2.88.9"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Tmds.DBus.Protocol/0.21.2": {"runtime": {"lib/net8.0/Tmds.DBus.Protocol.dll": {"assemblyVersion": "0.21.2.0", "fileVersion": "0.21.2.0"}}}, "Cadence.Application/1.0.0": {"dependencies": {"Cadence.Domain": "1.0.0"}, "runtime": {"Cadence.Application.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Cadence.Client/1.0.0": {"dependencies": {"Cadence.Application": "1.0.0"}, "runtime": {"Cadence.Client.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Cadence.Domain/1.0.0": {"runtime": {"Cadence.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Cadence.Infrastructure/1.0.0": {"dependencies": {"Cadence.Application": "1.0.0"}, "runtime": {"Cadence.Infrastructure.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Cadence.Rendering/1.0.0": {"dependencies": {"Silk.NET.WebGPU": "2.22.0", "Silk.NET.WebGPU.Native.WGPU": "2.22.0"}, "runtime": {"Cadence.Rendering.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Cadence.UI/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Avalonia/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-gWy26+7xDbGOURVUjion2lzqXEMo2+YOjVMhYn7ZGQNAAEjogK5EpVn6KNzeBcLUK+TNulkoZY7tjlHPSY2oFw==", "path": "avalonia/11.3.4", "hashPath": "avalonia.11.3.4.nupkg.sha512"}, "Avalonia.Angle.Windows.Natives/2.1.25547.20250602": {"type": "package", "serviceable": true, "sha512": "sha512-ZL0VLc4s9rvNNFt19Pxm5UNAkmKNylugAwJPX9ulXZ6JWs/l6XZihPWWTyezaoNOVyEPU8YbURtW7XMAtqXH5A==", "path": "avalonia.angle.windows.natives/2.1.25547.20250602", "hashPath": "avalonia.angle.windows.natives.2.1.25547.20250602.nupkg.sha512"}, "Avalonia.Controls.ColorPicker/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-9ZDFgEPLKGTvsyVgqcExSN1XzvRDqfzHILC3tQU08QL0o3p62w1PR008+D5YvbVPiZIlp2ugcT14dF1/n2+Eug==", "path": "avalonia.controls.colorpicker/11.3.4", "hashPath": "avalonia.controls.colorpicker.11.3.4.nupkg.sha512"}, "Avalonia.Desktop/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-wKRK2DDXT8xLgQEElOUe+91EO0sNDuplNhCMxjkHLvIIx/hS+Y/BqG0vsIRvkVyYT+sFVD9y3j5c6XTPAuJOsw==", "path": "avalonia.desktop/11.3.4", "hashPath": "avalonia.desktop.11.3.4.nupkg.sha512"}, "Avalonia.Diagnostics/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-7agu3YsJ3VjSKlGehh3CBMHm6wkMTMmXj4rqYt0Lc08ZVaziurezP8R2Hudsn0I9jS28X3WvpNBFuny7KCCWPw==", "path": "avalonia.diagnostics/11.3.4", "hashPath": "avalonia.diagnostics.11.3.4.nupkg.sha512"}, "Avalonia.Fonts.Inter/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-IhsWo4nrow1h56rgbJsEDFy8GHKLVvS9ux7MWOoCi169y4UB1SVD0LvgEwFszfqPLIBm5BvgyJCyCMnfVr6SGw==", "path": "avalonia.fonts.inter/11.3.4", "hashPath": "avalonia.fonts.inter.11.3.4.nupkg.sha512"}, "Avalonia.FreeDesktop/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-qvFjQhhxmpz/75tGTV8k5WqQBv5ZtJhmwgISUh0AAQCHJu4lIkuQEL3by3Y9uMat5nBtBFkvUNbb+okr6Rq83g==", "path": "avalonia.freedesktop/11.3.4", "hashPath": "avalonia.freedesktop.11.3.4.nupkg.sha512"}, "Avalonia.Native/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-/z1jskU1K0JF+yUI+NVfKgf+T42vs7wU4B2nC7SX7766j6DZWCin7Po8pb2EVrKdHseR+SUK0pYbztg9U3ge+w==", "path": "avalonia.native/11.3.4", "hashPath": "avalonia.native.11.3.4.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-WEb44B/h8uaqsFlHFHbJIUYcOJF1P6fWoVKFmTSpG8oxywl5+oNjJ1K51jL6FYhmzYapWoVq2INaaXMc7qngDA==", "path": "avalonia.remote.protocol/11.3.4", "hashPath": "avalonia.remote.protocol.11.3.4.nupkg.sha512"}, "Avalonia.Skia/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-FWo1I0tGa3fncpZzx/GoAKct2j2d5TmVNX8DngkLa6XBWaPvP6/8GST0L2J/Xdmsgj3/tEk90j+SW87eNiQFdg==", "path": "avalonia.skia/11.3.4", "hashPath": "avalonia.skia.11.3.4.nupkg.sha512"}, "Avalonia.Themes.Fluent/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-3HPGIWSYrYFIdgpOp4UzZd7ahCKjpDsakPo4g/TAlTWpQmwUmA6vQe7udEjwRHHvKjbzVrdg2bal6bizI87sMg==", "path": "avalonia.themes.fluent/11.3.4", "hashPath": "avalonia.themes.fluent.11.3.4.nupkg.sha512"}, "Avalonia.Themes.Simple/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-JIR/uTlyFD1EX2WoxD/8O1fwNbOimKYxzlaK+lZiW1ZG+5fcqEfG12Ve4/a/q54dWAAEISgLRjSgpCEywPqFuw==", "path": "avalonia.themes.simple/11.3.4", "hashPath": "avalonia.themes.simple.11.3.4.nupkg.sha512"}, "Avalonia.Win32/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-Gvl3C40BhgoYYauQwriN03w2Y9zqUfnE9TrITX7Sq3mpSSGitlAWsp4q2DUKiiFPrk0a51P7ygWDMwik39+Zsg==", "path": "avalonia.win32/11.3.4", "hashPath": "avalonia.win32.11.3.4.nupkg.sha512"}, "Avalonia.X11/11.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-IRyLbhXUaYGAb/o4MKWklbywCqLpDR4lqcSbn7yut/ji72odHwo5cCX3mjMqlpC5gVkQolMD6Cg+EjHTq/w+1Q==", "path": "avalonia.x11/11.3.4", "hashPath": "avalonia.x11.11.3.4.nupkg.sha512"}, "HarfBuzzSharp/*******": {"type": "package", "serviceable": true, "sha512": "sha512-tLZN66oe/uiRPTZfrCU4i8ScVGwqHNh5MHrXj0yVf4l7Mz0FhTGnQ71RGySROTmdognAs0JtluHkL41pIabWuQ==", "path": "harfbuzzsharp/*******", "hashPath": "harfbuzzsharp.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"type": "package", "serviceable": true, "sha512": "sha512-3EZ1mpIiKWRLL5hUYA82ZHteeDIVaEA/Z0rA/wU6tjx6crcAkJnBPwDXZugBSfo8+J3EznvRJf49uMsqYfKrHg==", "path": "harfbuzzsharp.nativeassets.linux/*******", "hashPath": "harfbuzzsharp.nativeassets.linux.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"type": "package", "serviceable": true, "sha512": "sha512-jbtCsgftcaFLCA13tVKo5iWdElJScrulLTKJre36O4YQTIlwDtPPqhRZNk+Y0vv4D1gxbscasGRucUDfS44ofQ==", "path": "harfbuzzsharp.nativeassets.macos/*******", "hashPath": "harfbuzzsharp.nativeassets.macos.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"type": "package", "serviceable": true, "sha512": "sha512-UsJtQsfAJoFDZrXc4hCUfRPMqccfKZ0iumJ/upcUjz/cmsTgVFGNEL5yaJWmkqsuFYdMWbj/En5/kS4PFl9hBA==", "path": "harfbuzzsharp.nativeassets.win32/*******", "hashPath": "harfbuzzsharp.nativeassets.win32.*******.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-jek4XYaQ/PGUwDKKhwR8K47Uh1189PFzMeLqO83mXrXQVIpARZCcfuDedH50YDTepBkfijCZN5U/vZi++erxtg==", "path": "microsoft.dotnet.platformabstractions/3.1.6", "hashPath": "microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Silk.NET.Core/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-XbDilPmvsQIrvxZkeVCNNX3LyRtA3hN6UykAiyZjlu/JkYh68U3WQummpP+j7jsxlX8s/pOrrU3vbN8LnMl0VA==", "path": "silk.net.core/2.22.0", "hashPath": "silk.net.core.2.22.0.nupkg.sha512"}, "Silk.NET.WebGPU/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-exCUhXxBWFlW8pV6krbf5K1ZwbhDtBM2qZa1HJa76CZ4RaMml5crmb1m+9qYERPhiQIEvK0EulCMs6CJ5p8bKQ==", "path": "silk.net.webgpu/2.22.0", "hashPath": "silk.net.webgpu.2.22.0.nupkg.sha512"}, "Silk.NET.WebGPU.Native.WGPU/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-b8D5q0HMYL85+vXzudS7VzTwANzOWJqA14EgMIIGd0YqZTbH8tOvk8ND1WZ2G4pEMtbvF8kYpf8kzTzo3g1Qqw==", "path": "silk.net.webgpu.native.wgpu/2.22.0", "hashPath": "silk.net.webgpu.native.wgpu.2.22.0.nupkg.sha512"}, "SkiaSharp/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "path": "skiasharp/2.88.9", "hashPath": "skiasharp.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-cWSaJKVPWAaT/WIn9c8T5uT/l4ETwHxNJTkEOtNKjphNo8AW6TF9O32aRkxqw3l8GUdUo66Bu7EiqtFh/XG0Zg==", "path": "skiasharp.nativeassets.linux/2.88.9", "hashPath": "skiasharp.nativeassets.linux.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "path": "skiasharp.nativeassets.macos/2.88.9", "hashPath": "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "path": "skiasharp.nativeassets.win32/2.88.9", "hashPath": "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512"}, "Tmds.DBus.Protocol/0.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-ScSMrUrrw8px4kK1Glh0fZv/HQUlg1078bNXNPfRPKQ3WbRzV9HpsydYEOgSoMK5LWICMf2bMwIFH0pGjxjcMA==", "path": "tmds.dbus.protocol/0.21.2", "hashPath": "tmds.dbus.protocol.0.21.2.nupkg.sha512"}, "Cadence.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Cadence.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Cadence.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Cadence.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Cadence.Rendering/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}