<UserControl xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:vm="clr-namespace:Cadence.UI.Views.ViewModels"
    x:Class="Cadence.UI.Views.HeaderBarView"
    x:DataType="vm:HeaderBarViewModel"
    MinHeight="48">
    <Border Background="{Binding Background}"
        BorderBrush="{DynamicResource ThemeBorderLowBrush}"
        BorderThickness="0,0,0,1"
        Padding="12,8">
        <Grid ColumnDefinitions="*,Auto,Auto" ColumnSpacing="12">
            <TextBlock Text="{Binding ProjectTitle}" FontWeight="SemiBold" FontSize="16" />
        </Grid>
    </Border>
</UserControl>