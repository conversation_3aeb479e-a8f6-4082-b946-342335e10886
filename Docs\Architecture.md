# Cadence Architecture Standards & Cheat Sheet

## 1\. Project Overview

Cadence is a native-first, cross-platform project management application. It features a GPU-accelerated interface (WebGPU) for dynamic, layered visualization of complex projects through a panel-based UI, built using Avalonia (Desktop and WASM).

## 2\. Architectural Principles

Cadence adheres strictly to **Clean Architecture** principles, emphasizing the separation of concerns and the **Dependency Inversion Principle (DIP)**.

- **The Dependency Rule:** Source code dependencies must only point inwards, toward higher-level policies (Domain/Application). The Core (Domain and Application) must be independent of frameworks, UI, and infrastructure details.
- **Unidirectional Data Flow:** Client state management follows a strict Redux pattern. State changes are predictable, and side effects are isolated.
- **Abstraction (Ports and Adapters):** The `Application` layer defines interfaces (Ports) that the `Infrastructure` layer implements (Adapters), keeping infrastructure details decoupled from business logic.
- **Modularity:** High-complexity components, such as the GPU rendering engine, are isolated into dedicated projects.

## 3\. Project Layers and Responsibilities

The solution is organized into the following layers and projects:

| Layer              | Project                  | Responsibility                                                                                                 | Depends On                                |
| :----------------- | :----------------------- | :------------------------------------------------------------------------------------------------------------- | :---------------------------------------- |
| **Core**           | `Cadence.Domain`         | Entities, Aggregates, Domain Events, pure business rules (e.g., scheduling algorithms).                        | (Nothing)                                 |
| **Core**           | `Cadence.Application`    | Use cases (Commands/Queries), Workflows, Interfaces (Ports) for infrastructure (e.g., `IProjectRepository`).   | `Domain`                                  |
| **Services**       | `Cadence.Client`         | Store (Redux state), Input Handling, Application State management, Middleware for side effects.                | `Application`                             |
| **Services**       | `Cadence.Rendering`      | Low-level WebGPU implementation, Shaders, Pipelines, Passes. The graphics engine.                              | (Independent)                             |
| **Infrastructure** | `Cadence.Infrastructure` | Implementation of persistence (SQLite/EF Core), logging (Serilog), configuration, external services.           | `Application` (Implements its interfaces) |
| **Presentation**   | `Cadence.UI`             | Views, ViewModels (MVVM), Avalonia Hosts (Desktop/Web). The Composition Root where all dependencies are wired. | All others (for DI)                       |

## 4\. Dependency Graph

Arrows indicate the direction of dependency. Note that Infrastructure depends on Application (to implement its interfaces), adhering to the Dependency Inversion Principle.

```mermaid
---
config:
    layout: elk
---
flowchart TB
    %% Layers
    subgraph Presentation
        UI[Cadence.UI Hosts]
    end

    subgraph Infrastructure_Layer [Infrastructure]
        Infra[Cadence.Infrastructure]
    end

    subgraph ApplicationServices [Application Services]
        Client[Cadence.Client]
        Rendering[Cadence.Rendering]
    end

    subgraph ApplicationLayer [Application Core]
        App[Cadence.Application]
    end

    subgraph DomainLayer [Domain]
        Domain[Cadence.Domain]
    end

    %% Dependencies (Arrows mean "depends on")

    %% Core Logic
    App --> Domain

    %% Infrastructure implements Application interfaces (Ports)
    Infra -- Implements Interfaces --> App

    %% Application Services
    Client --> App
    Client -- Orchestrates --> Rendering

    %% UI is the Composition Root, wiring everything together
    UI --> Client
    UI --> Rendering
    UI -. DI Wiring .-> Infra


    %% Styling
    classDef domain fill:#E6F3FF,stroke:#333,stroke-width:3px;
    classDef app fill:#CCE6FF,stroke:#333,stroke-width:2px;
    class Domain domain;
    class App app;
```

## 5\. Project Hierarchy (Directory Structure)

The directory structure is organized by layer to reflect the
architecture.

```text
Cadence/                                 — repository root
├─ Source/
│  ├─ Cadence.Domain/                    — pure business logic
│  │  ├─ Concepts/                        — entities, aggregates (Project, Task)
│  │  ├─ Rules/                           — pure algorithms (scheduling, critical path)
│  │  └─ Events/                          — domain events (TaskAdded, DependencyChanged)
│  │
│  ├─ Cadence.Application/               — orchestration and use cases
│  │  ├─ Interfaces/ (Ports)              — abstractions for Infra (IRepository, ILogger)
│  │  ├─ Policies/                        — global rules (autosave, recalculation triggers)
│  │  └─ Workflows/                       — orchestrations (import, export, sync pipelines)
│  │
│  ├─ Cadence.Client/                    — state + interactions
│  │  ├─ Store/                           — Redux (Actions, Reducers, Selectors)
│  │  ├─ Relay/                           — CRITICAL: Handles all side-effects
│  │  ├─ Input/                           — (Devices, Mapping, Tools)
│  │  └─ Orchestration/                   — coordinates state updates with Rendering
│  │
│  ├─ Cadence.Rendering/                 — WebGPU implementation
│  │  ├─ Device/                          — GPU device/context setup, swapchain, adapters
│  │  ├─ Pipelines/
│  │  │  ├─ Passes/                       — layered rendering passes
│  │  │  ├─ Shaders/                      — WGSL sources
│  │  │  ├─ Geometry/                     — WGSL for shapes, lines, task rendering
│  │  │  └─ Effects/                      — WGSL for effects + filters
│  │  └─ Interop/                         — (Desktop/Avalonia surface, Web/WASM bindings)
│  │
│  ├─ Cadence.Infrastructure/            — adapters
│  │  ├─ Persistence/
│  │  │  ├─ Sqlite/                        — EF Core/SQLite backend
│  │  │  ├─ Browser/                       — SQLite WASM + OPFS
│  │  │  └─ Repositories/                  — Implementation of IRepository
│  │  ├─ Logging/                         — Serilog implementation
│  │  └─ Platform/                        — Config, Time providers
│  │
│  └─ Cadence.UI/                        — views, controls, hosting
│     ├─ Desktop/                         — Avalonia desktop (Composition Root)
│     ├─ Web/                             — Avalonia WASM (Composition Root)
│     ├─ Views/
│     │  └─ ViewModels/
│     └─ Controls/                        — (GpuView hosting the Rendering surface)
```

## 6\. Runtime Data Flow (Redux/Unidirectional)

The application follows a strict unidirectional data flow.

1.  **Input/Intent:** User input (via `Client.Input` or `UI.ViewModels`) dispatches an "Intent" Action (e.g., `AttemptAddTask`) to the `Client.Store`.
2.  **Side Effects (Relay):** `Client.Relay` intercepts the Action. **This is the only place side effects occur.** Relay invokes `Application` layer Commands/Queries.
3.  **Business Logic:** The `Application` layer orchestrates the `Domain` logic and interacts with `Infrastructure` via interfaces (e.g., `IRepository`).
4.  **State Update (Reducers):** After the side effect completes, the Relay dispatches a "Result" Action (e.g., `TaskAddedSuccess`). `Client.Reducers` (which must be **pure functions**) process this Action and update the state in the `Store`.
5.  **Presentation:** The `Store` notifies subscribers. `ViewModels` and the `Client.Orchestration` use Selectors to read the new state, triggering UI updates and `Rendering` engine draw calls.

<!-- end list -->

```mermaid
---
config:
    layout: elk
---
flowchart LR
    %% 1. Input Phase
    User["User Input"] --> Input[Cadence.Client.Input]
    UIActions["UI Actions"] --> ViewModels[Cadence.UI.ViewModels]

    Input -- "Dispatch Intent Action" --> Store[Cadence.Client.Store]
    ViewModels -- "Dispatch Intent Action" --> Store

    %% 2. Side Effect Phase (Handled by Relay)
    Store -- Action --> Relay[Client.Relay]
    Relay -- "Invoke Use Case (Command/Query)" --> AppLayer[Cadence.Application]

    %% 3. Business Logic Phase
    AppLayer -- "Orchestrates" --> Domain[Cadence.Domain]
    AppLayer -- "Uses IRepository" --> Infra[Cadence.Infrastructure.Persistence]
    Domain -. State Change .-> AppLayer

    %% 4. State Update Phase (Pure Functions)
    AppLayer -- Return Result --> Relay
    Relay -- "Dispatch Result Action (e.g., Success/Failure)" --> Reducer[Client.Reducers]
    Reducer -- "Pure State Update" --> Store

    %% 5. Presentation Update Phase
    Store -- Selectors --> ViewModels
    Store -- Selectors --> Orchestration[Client.Orchestration]
    Orchestration --> Rendering[Cadence.Rendering] --> Views["Cadence.UI.Views (GPU Surface)"]
```

## 7\. Patterns and Technologies

| Project            | Key Patterns                                            | Key Technologies/Frameworks                             |
| :----------------- | :------------------------------------------------------ | :------------------------------------------------------ |
| **Domain**         | DDD (Aggregates, Entities, VOs), Domain Events          | Pure C\#/.NET                                           |
| **Application**    | CQRS, Mediator, Ports (Interfaces)                      | `MediatR` (Optional)                                    |
| **Client**         | Redux (Unidirectional flow), Relay, Selectors, Observer | `System.Reactive` (Rx.NET), DI                          |
| **Rendering**      | Layered Rendering Pipeline, Shader Management, Interop  | WebGPU bindings (e.g., `Silk.NET`), WGSL                |
| **Infrastructure** | Adapter Pattern, Repository Pattern                     | `EF Core`, `SQLite`, `sqlite-wasm` (Browser), `Serilog` |
| **UI**             | MVVM, Composition Root (DI)                             | `Avalonia`, `ReactiveUI`                                |

## 8\. Coding Standards and Conventions

Consistency is enforced via `.editorconfig` and Roslyn analyzers located in the `/eng` directory.

### 8.1. C\# Features and Style

- **Nullable Reference Types (NRT):** NRT is enabled solution-wide (`<Nullable>enable</Nullable>`). All warnings must be treated as errors. Strive for zero nullability warnings and avoid the null-forgiving operator (`!`) unless strictly necessary for framework interop.
- **Immutability:** Prefer immutability wherever possible.
  - Use C\# `record` types for DTOs, Commands, Queries, Events, and Redux State objects.
  - Use the `with` expression for non-destructive mutation (e.g., `return state with { IsLoading = true };`).
- **Purity:** Domain logic (`Cadence.Domain`) and Reducers (`Cadence.Client.Store.Reducers`) must be pure functions—they must not cause side effects and must be deterministic.
- **File Structure:** Adhere to one primary type per file, matching the filename to the type name.

### 8.2. Asynchronicity

- **`async/await` Everywhere:** Use `async/await` for all I/O-bound operations. Never block synchronously using `.Result` or `.Wait()`.
- **Cancellation Tokens:** All asynchronous methods must accept and pass through a `CancellationToken` as the last parameter.
- **Configuration:** Use `.ConfigureAwait(false)` in library code (Domain, Application, Client, Infrastructure, Rendering) to avoid deadlocks. It is generally omitted only in the top-level `Cadence.UI` layer where synchronization context is required.

### 8.3. Naming Conventions

| Type                        | Convention                         | Example                                       | Notes                                 |
| :-------------------------- | :--------------------------------- | :-------------------------------------------- | :------------------------------------ |
| **Interfaces**              | PascalCase prefixed with `I`       | `IProjectRepository`                          |                                       |
| **Classes/Records/Structs** | PascalCase                         | `TaskSchedulerService`                        |                                       |
| **Async Methods**           | Suffix with `Async`                | `LoadProjectAsync`                            | Must accept `CancellationToken`.      |
| **Private Fields**          | camelCase prefixed with `_`        | `_logger`, `_projectId`                       |                                       |
| **Domain Events**           | PascalCase, Past Tense             | `ProjectCreated`                              | Indicates something happened.         |
| **Application Commands**    | PascalCase, Imperative Verb        | `CreateProjectCommand`                        | Instructs the system to do something. |
| **Redux Actions (Intent)**  | PascalCase, Descriptive/Imperative | `Tasks_CreateRequested`                       | Dispatched by UI/Input to Relay.      |
| **Redux Actions (Result)**  | PascalCase, Success/Failure        | `Tasks_CreateSucceeded`, `Tasks_CreateFailed` | Dispatched by Relay to Reducers.      |
| **Redux Selectors**         | Prefix with `Select`               | `SelectActiveProject`                         | Queries derived state.                |

### 8.4. File Organization (Feature Slicing)

Within `Cadence.Application` and `Cadence.Client`, organize code into **Feature Folders** rather than grouping by technical type.

```text
Cadence.Application/
 ├─ Features/
 │  ├─ Tasks/
 │  │  ├─ Commands/
 │  │  │  ├─ CreateTaskCommand.cs
 │  │  │  └─ CreateTaskCommandHandler.cs
 │  │  └─ Queries/
...
```

## 9\. Implementation Idioms and Patterns

This details how specific architectural patterns are implemented within Cadence.

### 9.1. Domain-Driven Design (Cadence.Domain)

- **Invariants:** Entities must protect their own invariants and always be in a valid state.
- **Aggregates:** Use Aggregate Roots (e.g., `Project`) to enforce transactional consistency. Access to internal entities must go through the Root.
- **Value Objects:** Prefer strongly-typed Value Objects over primitive types for IDs and concepts (e.g., use a `ProjectId` record instead of `Guid`).

### 9.2. CQRS (Cadence.Application)

- **Framework:** Use the `MediatR` library for in-process messaging.
- **Validation:** Command validation uses `FluentValidation` and is implemented as a MediatR pipeline behavior.
- **Interfaces (Ports):** Interfaces for external dependencies (e.g., `IProjectRepository`, `ITimeProvider`) must be defined here.

**CQRS Implementation Idiom (Conceptual Example):**

```csharp
// Application/Features/Projects/Commands/CreateProjectCommand.cs
public record CreateProjectCommand(string Name) : IRequest<Result<Guid>>;

// Application/Features/Projects/Commands/CreateProjectCommandHandler.cs
public class CreateProjectHandler : IRequestHandler<CreateProjectCommand, Result<Guid>>
{
    private readonly IProjectRepository _repository;
    // ... constructor injection ...

    public async Task<Result<Guid>> Handle(CreateProjectCommand request, CancellationToken ct)
    {
        // Domain Logic (Domain layer handles creation and invariants)
        var projectResult = Project.Create(request.Name);
        if (projectResult.IsFailure)
        {
            // Return failure Result, do not throw exception for business rules
            return Result.Fail(projectResult.Error);
        }

        // Persistence (via Infrastructure adapter)
        await _repository.AddAsync(projectResult.Value, ct);

        return Result.Success(projectResult.Value.Id);
    }
}
```

### 9.3. Redux (Cadence.Client)

- **State Structure:** The state should be normalized (flattened) and immutable.
- **Reducers:** Must be **pure functions**.
- **Relay:** Handles **all** side effects. It bridges the gap between UI intents and Application logic.

**Redux Implementation Idiom (Conceptual Example):**

```csharp
// 1. Define Actions (Client/Store/Actions)
public record Projects_CreateRequested(string Name);
public record Projects_CreateSucceeded(ProjectDto Project);
public record Projects_CreateFailed(string ErrorMessage);

// 2. Implement Relay (Client/Relay) - Handles Side Effects
public async Task HandleProjectCreation(Projects_CreateRequested action, IDispatcher dispatcher)
{
    // Side effect: Calling the Application Layer (via MediatR)
    var result = await _mediator.Send(new CreateProjectCommand(action.Name));

    if (result.IsSuccess)
        // Map result to DTO and dispatch Success
        dispatcher.Dispatch(new Projects_CreateSucceeded(MapToDto(result.Value)));
    else
        // Dispatch Failure
        dispatcher.Dispatch(new Projects_CreateFailed(result.Error));
}

// 3. Implement Reducer (Client/Store/Reducers) - Pure State Update
public static ProjectState ReduceCreateSucceeded(ProjectState state, Projects_CreateSucceeded action)
{
    // Purity: Returning a NEW state object using 'with'
    var updatedProjects = state.Projects.Add(action.Project.Id, action.Project);
    return state with { Projects = updatedProjects, IsLoading = false };
}
```

### 9.4. MVVM (Cadence.UI)

- **Framework:** Use `Avalonia` with `ReactiveUI`.
- **ViewModels:** ViewModels must be thin. They adapt the `Client.Store` state (via Selectors) for the View and dispatch Actions to the `Client.Store`. They **do not** call the `Application` layer directly.

## 10\. Cross-Cutting Concerns

### 10.1. Error Handling Strategy

- **Result Pattern (Application Layer):** Do **not** throw exceptions for business rule violations or expected failures (e.g., validation errors, "item not found"). Use the **Result Pattern** (e.g., `FluentResults` or a custom `Result<T>`).
- **Exceptions (Exceptional Cases):** Exceptions should only be thrown for truly exceptional infrastructure failures (e.g., disk full, network connection lost) in the `Infrastructure` layer, or for invariant violations in the `Domain`.
- **Client Handling:** `Client.Middleware` must use try/catch blocks around Application/Infrastructure calls and translate failures (both Result failures and Exceptions) into Redux "Failure" actions.

### 10.2. Logging

- Use the `ILogger<T>` interface provided via DI (implemented by `Serilog` in `Infrastructure`).
- Utilize **Structured Logging**.
  - _Bad:_ `_logger.LogInformation($"User {userId} logged in.");`
  - _Good:_ `_logger.LogInformation("User {UserId} logged in.", userId);`

### 10.3. Dependency Injection (DI)

- **Registration:** Each project exposes extension methods (e.g., `AddApplicationServices()`).
- **Composition:** The `Cadence.UI` hosts are the Composition Roots.
- **Consumption:** Use Constructor Injection exclusively. Avoid the Service Locator pattern.
- **Lifetimes:** Prefer `Transient` for application handlers. Use `Singleton` for the Store, Rendering context, and stateless infrastructure.

### 10.4. Time Abstraction

- Do not use `DateTime.Now` or `DateTime.UtcNow` directly in business logic. Inject and use an `ITimeProvider` interface (defined in `Application.Interfaces`) or the modern .NET `TimeProvider` abstraction.

## 11\. Concurrency and Threading Model

- **UI Thread (Avalonia):** Never block the main UI thread. Use `ReactiveUI` schedulers (`ObserveOn(RxApp.MainThreadScheduler)`) or the `Dispatcher` when marshaling updates from background threads.
- **Rendering Loop:** `Cadence.Rendering` operates on its own high-frequency frame loop.
- **Client-Rendering Communication:** Communication between `Cadence.Client` (state) and `Cadence.Rendering` must be thread-safe. This is managed by `Client.Orchestration`, typically by passing immutable snapshots of the required render state across the thread boundary.
- **Reactive Extensions (Rx.NET):** Use Rx for complex asynchronous event streams, input handling (throttling, debouncing), and UI orchestration.

## 12\. Testing Strategy

Test projects are located in the `/Tests` directory and mirror the source project names.

### 12.1. Frameworks

- **Runner:** xUnit
- **Assertions:** FluentAssertions
- **Mocking:** NSubstitute (or Moq)

### 12.2. Testing by Layer

| Project                  | Test Type        | Focus Areas                                                                  | Mocking Policy                                                                 |
| :----------------------- | :--------------- | :--------------------------------------------------------------------------- | :----------------------------------------------------------------------------- |
| **Domain.Tests**         | Unit (Pure)      | Complex rules, algorithms, entity invariants.                                | **No Mocking.** Test using real inputs.                                        |
| **Application.Tests**    | Unit/Integration | Command/Query Handlers, workflow orchestration.                              | Mock infrastructure Ports (e.g., `IProjectRepository`, `ITimeProvider`).       |
| **Client.Tests**         | Unit/Integration | Reducers (purity), Selectors. Relay orchestration.                           | Test Reducers with real state/actions. Mock Application layer for Relay tests. |
| **Infrastructure.Tests** | Integration      | Repository implementations against a real database (e.g., SQLite in-memory). | Requires test infrastructure.                                                  |
| **UI.Tests**             | Unit             | ViewModel logic, bindings.                                                   | Mock the `Client.Store`.                                                       |

### 12.3. Test Conventions

- **Structure:** Follow the Arrange, Act, Assert (AAA) pattern strictly.
- **Naming:** `[MethodName]_[Scenario]_[ExpectedResult]`
  - Example: `CalculateCriticalPath_WithDependencies_ReturnsCorrectPath`
- **Assertions:** Always use `FluentAssertions`.
  - _Bad:_ `Assert.Equal(5, result.Count);`
  - _Good:_ `result.Should().HaveCount(5);`
