{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Cadence.Infrastructure/1.0.0": {"dependencies": {"Cadence.Application": "1.0.0"}, "runtime": {"Cadence.Infrastructure.dll": {}}}, "Cadence.Application/1.0.0": {"dependencies": {"Cadence.Domain": "1.0.0"}, "runtime": {"Cadence.Application.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Cadence.Domain/1.0.0": {"runtime": {"Cadence.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Cadence.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Cadence.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Cadence.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}