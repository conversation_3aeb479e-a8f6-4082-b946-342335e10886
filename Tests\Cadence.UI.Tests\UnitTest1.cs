﻿using Cadence.UI.Views.ViewModels;

namespace Cadence.UI.Tests;

public class UnitTest1
{
    [Fact]
    public void BackgroundUpdatesRaiseChangeNotification()
    {
        using var vm = new SidePanelViewModel();
        bool raised = false;
        vm.PropertyChanged += (s, e) => { if (e.PropertyName == nameof(SidePanelViewModel.Background)) raised = true; };
        vm.SetBackgroundFromPixels(new byte[] { 0, 0, 0, 255 }, 1, 1);
        Assert.True(raised);
        Assert.NotNull(vm.Background);
    }
}
