using System.Threading;
using System.Threading.Tasks;
using Cadence.Rendering.Device;

namespace Cadence.Rendering.Services;

public sealed class NullOffscreenRenderer : IOffscreenRenderer
{
    public Task<byte[]> RenderBgra8Async(string shaderId, int width, int height, CancellationToken cancellationToken)
    {
        // Transparent black buffer as a safe fallback
        return Task.FromResult(new byte[width * height * 4]);
    }

    public void Dispose()
    {
        // No resources
    }
}

