// Flat color for Timeline
@vertex
fn vs_main(@builtin(vertex_index) vi : u32) -> @builtin(position) vec4f {
    var pos = array<vec2f, 6>(
        vec2f(-1.0, -1.0), vec2f(1.0, -1.0), vec2f(1.0, 1.0),
        vec2f(-1.0, -1.0), vec2f(1.0, 1.0), vec2f(-1.0, 1.0)
    );
    let p = pos[vi];
    return vec4f(p, 0.0, 1.0);
}

@fragment
fn fs_main() -> @location(0) vec4f {
    // Deep navy
    return vec4f(0.06, 0.08, 0.14, 1.0);
}


