<UserControl xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:vm="clr-namespace:Cadence.UI.Views.ViewModels"
    x:Class="Cadence.UI.Views.TimelineView"
    x:DataType="vm:TimelineViewModel">
    <Border Background="{Binding Background}"
        Padding="0">
        <Grid>
            <TextBlock Text="Timeline" Margin="12" />
            <!-- Placeholder for GPU surface hosting control to integrate Rendering later -->
        </Grid>
    </Border>
</UserControl>