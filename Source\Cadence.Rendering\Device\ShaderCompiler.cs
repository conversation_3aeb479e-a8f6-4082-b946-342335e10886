using System;
using System.IO;
using Silk.NET.Core.Native;
using Silk.NET.WebGPU;

namespace Cadence.Rendering.Device;

internal static unsafe class ShaderCompiler
{
    internal static ShaderModule* CreateShaderModuleFromWgsl(WebGPU wgpu, Silk.NET.WebGPU.Device* device, string wgslSource)
    {
        ArgumentNullException.ThrowIfNull(wgpu);
        if (device == null) throw new ArgumentNullException(nameof(device));
        if (string.IsNullOrWhiteSpace(wgslSource)) throw new ArgumentException("WGSL source must be provided.", nameof(wgslSource));
        // Prepare chained WGSL descriptor
        ShaderModuleWGSLDescriptor wgsl = new ShaderModuleWGSLDescriptor();
        wgsl.Chain.SType = SType.ShaderModuleWgslDescriptor;
        wgsl.Code = (byte*)SilkMarshal.StringToPtr(wgslSource);

        ShaderModuleDescriptor desc = new ShaderModuleDescriptor
        {
            NextInChain = (ChainedStruct*)&wgsl
        };

        var module = wgpu.DeviceCreateShaderModule(device, &desc);
        SilkMarshal.Free((nint)wgsl.Code);
        if (module is null)
        {
            throw new InvalidOperationException("Failed to create shader module from WGSL.");
        }
        return module;
    }

    public static string LoadWgslFromContent(string shaderId)
    {
        // Load from embedded resources: Pipelines/Shaders/**/{Name}.wgsl
        var asm = typeof(ShaderCompiler).Assembly;
        var suffix = Path.GetFileName(shaderId) + ".wgsl";
        foreach (var resName in asm.GetManifestResourceNames())
        {
            if (resName.EndsWith(suffix, StringComparison.OrdinalIgnoreCase))
            {
                using var stream = asm.GetManifestResourceStream(resName)!;
                using var reader = new StreamReader(stream);
                return reader.ReadToEnd();
            }
        }
        throw new FileNotFoundException($"WGSL embedded resource not found for '{shaderId}'.");
    }
}


