C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.UI.exe
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.UI.deps.json
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.UI.runtimeconfig.json
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.UI.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.UI.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Base.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Controls.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.DesignerSupport.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Dialogs.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Markup.Xaml.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Markup.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Metal.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.MicroCom.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.OpenGL.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Vulkan.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Controls.ColorPicker.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Desktop.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Diagnostics.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Fonts.Inter.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.FreeDesktop.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Native.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Remote.Protocol.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Skia.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Themes.Fluent.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Themes.Simple.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Win32.Automation.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.Win32.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Avalonia.X11.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\HarfBuzzSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\MicroCom.Runtime.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Microsoft.DotNet.PlatformAbstractions.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Microsoft.Extensions.DependencyModel.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Silk.NET.Core.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Silk.NET.WebGPU.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\SkiaSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Tmds.DBus.Protocol.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-arm64\native\av_libglesv2.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x64\native\av_libglesv2.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x86\native\av_libglesv2.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\osx\native\libAvaloniaNative.dylib
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-arm\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-arm64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-loongarch64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-musl-arm\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-musl-arm64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-musl-loongarch64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-musl-riscv64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-musl-x64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-riscv64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-x64\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-x86\native\libHarfBuzzSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\osx\native\libHarfBuzzSharp.dylib
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-arm64\native\libHarfBuzzSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x64\native\libHarfBuzzSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x86\native\libHarfBuzzSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-arm\native\libSkiaSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-arm64\native\libSkiaSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-musl-x64\native\libSkiaSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-x64\native\libSkiaSharp.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\osx\native\libSkiaSharp.dylib
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-arm64\native\libSkiaSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x64\native\libSkiaSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x86\native\libSkiaSharp.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Application.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Client.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Domain.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Infrastructure.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Rendering.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Client.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Rendering.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Infrastructure.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Application.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\Cadence.Domain.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.csproj.AssemblyReference.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Avalonia\Resources.Inputs.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Avalonia\resources
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.GeneratedMSBuildEditorConfig.editorconfig
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.AssemblyInfoInputs.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.AssemblyInfo.cs
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.csproj.CoreCompileInputs.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.csproj.Up2Date
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\refint\Cadence.UI.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.pdb
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\Cadence.UI.genruntimeconfig.cache
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Intermediate\Debug\net9.0\ref\Cadence.UI.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-arm\native\libwgpu_native.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-arm64\native\libwgpu_native.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\linux-x64\native\libwgpu_native.so
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\osx-arm64\native\libwgpu_native.dylib
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\osx-x64\native\libwgpu_native.dylib
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-arm64\native\wgpu_native.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x64\native\wgpu_native.dll
C:\Dev\Projects\Cadence\Source\Cadence.UI\Build\Debug\net9.0\runtimes\win-x86\native\wgpu_native.dll
