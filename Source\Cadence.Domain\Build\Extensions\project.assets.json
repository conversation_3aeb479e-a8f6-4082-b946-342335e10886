{"version": 3, "targets": {"net9.0": {"Microsoft.CodeAnalysis.NetAnalyzers/9.0.0": {"type": "package", "build": {"buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.props": {}, "buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.targets": {}}}}}, "libraries": {"Microsoft.CodeAnalysis.NetAnalyzers/9.0.0": {"sha512": "JajbvkrBgtdRghavIjcJuNHMOja4lqBmEezbhZyqWPYh2cpLhT5mPpfC7NQVDO4IehWQum9t/nwF4v+qQGtYWg==", "type": "package", "path": "microsoft.codeanalysis.netanalyzers/9.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.NetAnalyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.NetAnalyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.NetAnalyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.NetAnalyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.NetAnalyzers.resources.dll", "buildTransitive/DisableNETAnalyzersForNuGetPackage.props", "buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.props", "buildTransitive/Microsoft.CodeAnalysis.NetAnalyzers.targets", "buildTransitive/config/analysislevel_10_all.globalconfig", "buildTransitive/config/analysislevel_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_default.globalconfig", "buildTransitive/config/analysislevel_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_minimum.globalconfig", "buildTransitive/config/analysislevel_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_none.globalconfig", "buildTransitive/config/analysislevel_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_10_recommended.globalconfig", "buildTransitive/config/analysislevel_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_all.globalconfig", "buildTransitive/config/analysislevel_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_default.globalconfig", "buildTransitive/config/analysislevel_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_minimum.globalconfig", "buildTransitive/config/analysislevel_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_none.globalconfig", "buildTransitive/config/analysislevel_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_5_recommended.globalconfig", "buildTransitive/config/analysislevel_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_all.globalconfig", "buildTransitive/config/analysislevel_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_default.globalconfig", "buildTransitive/config/analysislevel_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_minimum.globalconfig", "buildTransitive/config/analysislevel_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_none.globalconfig", "buildTransitive/config/analysislevel_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_6_recommended.globalconfig", "buildTransitive/config/analysislevel_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_all.globalconfig", "buildTransitive/config/analysislevel_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_default.globalconfig", "buildTransitive/config/analysislevel_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_minimum.globalconfig", "buildTransitive/config/analysislevel_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_none.globalconfig", "buildTransitive/config/analysislevel_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_7_recommended.globalconfig", "buildTransitive/config/analysislevel_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_all.globalconfig", "buildTransitive/config/analysislevel_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_default.globalconfig", "buildTransitive/config/analysislevel_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_minimum.globalconfig", "buildTransitive/config/analysislevel_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_none.globalconfig", "buildTransitive/config/analysislevel_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_8_recommended.globalconfig", "buildTransitive/config/analysislevel_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_all.globalconfig", "buildTransitive/config/analysislevel_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_default.globalconfig", "buildTransitive/config/analysislevel_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_minimum.globalconfig", "buildTransitive/config/analysislevel_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_none.globalconfig", "buildTransitive/config/analysislevel_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_9_recommended.globalconfig", "buildTransitive/config/analysislevel_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_all.globalconfig", "buildTransitive/config/analysisleveldesign_10_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_default.globalconfig", "buildTransitive/config/analysisleveldesign_10_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_none.globalconfig", "buildTransitive/config/analysisleveldesign_10_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_10_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_all.globalconfig", "buildTransitive/config/analysisleveldesign_5_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_default.globalconfig", "buildTransitive/config/analysisleveldesign_5_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_none.globalconfig", "buildTransitive/config/analysisleveldesign_5_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_5_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_all.globalconfig", "buildTransitive/config/analysisleveldesign_6_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_default.globalconfig", "buildTransitive/config/analysisleveldesign_6_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_none.globalconfig", "buildTransitive/config/analysisleveldesign_6_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_6_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_all.globalconfig", "buildTransitive/config/analysisleveldesign_7_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_default.globalconfig", "buildTransitive/config/analysisleveldesign_7_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_none.globalconfig", "buildTransitive/config/analysisleveldesign_7_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_7_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_all.globalconfig", "buildTransitive/config/analysisleveldesign_8_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_default.globalconfig", "buildTransitive/config/analysisleveldesign_8_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_none.globalconfig", "buildTransitive/config/analysisleveldesign_8_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_8_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_all.globalconfig", "buildTransitive/config/analysisleveldesign_9_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_default.globalconfig", "buildTransitive/config/analysisleveldesign_9_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_minimum.globalconfig", "buildTransitive/config/analysisleveldesign_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_none.globalconfig", "buildTransitive/config/analysisleveldesign_9_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldesign_9_recommended.globalconfig", "buildTransitive/config/analysisleveldesign_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_all.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_all_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_default.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_default_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_minimum.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_none.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_none_warnaserror.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_recommended.globalconfig", "buildTransitive/config/analysisleveldocumentation_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_all.globalconfig", "buildTransitive/config/analysislevelglobalization_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_default.globalconfig", "buildTransitive/config/analysislevelglobalization_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_none.globalconfig", "buildTransitive/config/analysislevelglobalization_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_10_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_all.globalconfig", "buildTransitive/config/analysislevelglobalization_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_default.globalconfig", "buildTransitive/config/analysislevelglobalization_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_none.globalconfig", "buildTransitive/config/analysislevelglobalization_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_5_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_all.globalconfig", "buildTransitive/config/analysislevelglobalization_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_default.globalconfig", "buildTransitive/config/analysislevelglobalization_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_none.globalconfig", "buildTransitive/config/analysislevelglobalization_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_6_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_all.globalconfig", "buildTransitive/config/analysislevelglobalization_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_default.globalconfig", "buildTransitive/config/analysislevelglobalization_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_none.globalconfig", "buildTransitive/config/analysislevelglobalization_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_7_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_all.globalconfig", "buildTransitive/config/analysislevelglobalization_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_default.globalconfig", "buildTransitive/config/analysislevelglobalization_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_none.globalconfig", "buildTransitive/config/analysislevelglobalization_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_8_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_all.globalconfig", "buildTransitive/config/analysislevelglobalization_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_default.globalconfig", "buildTransitive/config/analysislevelglobalization_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_minimum.globalconfig", "buildTransitive/config/analysislevelglobalization_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_none.globalconfig", "buildTransitive/config/analysislevelglobalization_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelglobalization_9_recommended.globalconfig", "buildTransitive/config/analysislevelglobalization_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_all.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_default.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_minimum.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_none.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_recommended.globalconfig", "buildTransitive/config/analysislevelinteroperability_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_all.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_default.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_minimum.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_none.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_recommended.globalconfig", "buildTransitive/config/analysislevelmaintainability_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_all.globalconfig", "buildTransitive/config/analysislevelnaming_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_default.globalconfig", "buildTransitive/config/analysislevelnaming_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_none.globalconfig", "buildTransitive/config/analysislevelnaming_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_10_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_all.globalconfig", "buildTransitive/config/analysislevelnaming_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_default.globalconfig", "buildTransitive/config/analysislevelnaming_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_none.globalconfig", "buildTransitive/config/analysislevelnaming_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_5_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_all.globalconfig", "buildTransitive/config/analysislevelnaming_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_default.globalconfig", "buildTransitive/config/analysislevelnaming_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_none.globalconfig", "buildTransitive/config/analysislevelnaming_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_6_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_all.globalconfig", "buildTransitive/config/analysislevelnaming_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_default.globalconfig", "buildTransitive/config/analysislevelnaming_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_none.globalconfig", "buildTransitive/config/analysislevelnaming_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_7_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_all.globalconfig", "buildTransitive/config/analysislevelnaming_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_default.globalconfig", "buildTransitive/config/analysislevelnaming_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_none.globalconfig", "buildTransitive/config/analysislevelnaming_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_8_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_all.globalconfig", "buildTransitive/config/analysislevelnaming_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_default.globalconfig", "buildTransitive/config/analysislevelnaming_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_minimum.globalconfig", "buildTransitive/config/analysislevelnaming_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_none.globalconfig", "buildTransitive/config/analysislevelnaming_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelnaming_9_recommended.globalconfig", "buildTransitive/config/analysislevelnaming_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_all.globalconfig", "buildTransitive/config/analysislevelperformance_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_default.globalconfig", "buildTransitive/config/analysislevelperformance_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_none.globalconfig", "buildTransitive/config/analysislevelperformance_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_10_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_all.globalconfig", "buildTransitive/config/analysislevelperformance_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_default.globalconfig", "buildTransitive/config/analysislevelperformance_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_none.globalconfig", "buildTransitive/config/analysislevelperformance_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_5_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_all.globalconfig", "buildTransitive/config/analysislevelperformance_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_default.globalconfig", "buildTransitive/config/analysislevelperformance_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_none.globalconfig", "buildTransitive/config/analysislevelperformance_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_6_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_all.globalconfig", "buildTransitive/config/analysislevelperformance_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_default.globalconfig", "buildTransitive/config/analysislevelperformance_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_none.globalconfig", "buildTransitive/config/analysislevelperformance_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_7_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_all.globalconfig", "buildTransitive/config/analysislevelperformance_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_default.globalconfig", "buildTransitive/config/analysislevelperformance_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_none.globalconfig", "buildTransitive/config/analysislevelperformance_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_8_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_all.globalconfig", "buildTransitive/config/analysislevelperformance_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_default.globalconfig", "buildTransitive/config/analysislevelperformance_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_minimum.globalconfig", "buildTransitive/config/analysislevelperformance_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_none.globalconfig", "buildTransitive/config/analysislevelperformance_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelperformance_9_recommended.globalconfig", "buildTransitive/config/analysislevelperformance_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_all.globalconfig", "buildTransitive/config/analysislevelreliability_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_default.globalconfig", "buildTransitive/config/analysislevelreliability_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_none.globalconfig", "buildTransitive/config/analysislevelreliability_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_10_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_all.globalconfig", "buildTransitive/config/analysislevelreliability_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_default.globalconfig", "buildTransitive/config/analysislevelreliability_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_none.globalconfig", "buildTransitive/config/analysislevelreliability_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_5_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_all.globalconfig", "buildTransitive/config/analysislevelreliability_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_default.globalconfig", "buildTransitive/config/analysislevelreliability_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_none.globalconfig", "buildTransitive/config/analysislevelreliability_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_6_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_all.globalconfig", "buildTransitive/config/analysislevelreliability_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_default.globalconfig", "buildTransitive/config/analysislevelreliability_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_none.globalconfig", "buildTransitive/config/analysislevelreliability_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_7_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_all.globalconfig", "buildTransitive/config/analysislevelreliability_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_default.globalconfig", "buildTransitive/config/analysislevelreliability_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_none.globalconfig", "buildTransitive/config/analysislevelreliability_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_8_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_all.globalconfig", "buildTransitive/config/analysislevelreliability_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_default.globalconfig", "buildTransitive/config/analysislevelreliability_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_minimum.globalconfig", "buildTransitive/config/analysislevelreliability_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_none.globalconfig", "buildTransitive/config/analysislevelreliability_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelreliability_9_recommended.globalconfig", "buildTransitive/config/analysislevelreliability_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_all.globalconfig", "buildTransitive/config/analysislevelsecurity_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_default.globalconfig", "buildTransitive/config/analysislevelsecurity_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_none.globalconfig", "buildTransitive/config/analysislevelsecurity_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_10_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_all.globalconfig", "buildTransitive/config/analysislevelsecurity_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_default.globalconfig", "buildTransitive/config/analysislevelsecurity_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_none.globalconfig", "buildTransitive/config/analysislevelsecurity_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_5_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_all.globalconfig", "buildTransitive/config/analysislevelsecurity_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_default.globalconfig", "buildTransitive/config/analysislevelsecurity_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_none.globalconfig", "buildTransitive/config/analysislevelsecurity_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_6_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_all.globalconfig", "buildTransitive/config/analysislevelsecurity_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_default.globalconfig", "buildTransitive/config/analysislevelsecurity_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_none.globalconfig", "buildTransitive/config/analysislevelsecurity_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_7_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_all.globalconfig", "buildTransitive/config/analysislevelsecurity_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_default.globalconfig", "buildTransitive/config/analysislevelsecurity_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_none.globalconfig", "buildTransitive/config/analysislevelsecurity_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_8_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_all.globalconfig", "buildTransitive/config/analysislevelsecurity_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_default.globalconfig", "buildTransitive/config/analysislevelsecurity_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_minimum.globalconfig", "buildTransitive/config/analysislevelsecurity_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_none.globalconfig", "buildTransitive/config/analysislevelsecurity_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelsecurity_9_recommended.globalconfig", "buildTransitive/config/analysislevelsecurity_9_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_all.globalconfig", "buildTransitive/config/analysislevelusage_10_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_default.globalconfig", "buildTransitive/config/analysislevelusage_10_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_minimum.globalconfig", "buildTransitive/config/analysislevelusage_10_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_none.globalconfig", "buildTransitive/config/analysislevelusage_10_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_10_recommended.globalconfig", "buildTransitive/config/analysislevelusage_10_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_all.globalconfig", "buildTransitive/config/analysislevelusage_5_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_default.globalconfig", "buildTransitive/config/analysislevelusage_5_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_minimum.globalconfig", "buildTransitive/config/analysislevelusage_5_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_none.globalconfig", "buildTransitive/config/analysislevelusage_5_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_5_recommended.globalconfig", "buildTransitive/config/analysislevelusage_5_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_all.globalconfig", "buildTransitive/config/analysislevelusage_6_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_default.globalconfig", "buildTransitive/config/analysislevelusage_6_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_minimum.globalconfig", "buildTransitive/config/analysislevelusage_6_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_none.globalconfig", "buildTransitive/config/analysislevelusage_6_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_6_recommended.globalconfig", "buildTransitive/config/analysislevelusage_6_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_all.globalconfig", "buildTransitive/config/analysislevelusage_7_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_default.globalconfig", "buildTransitive/config/analysislevelusage_7_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_minimum.globalconfig", "buildTransitive/config/analysislevelusage_7_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_none.globalconfig", "buildTransitive/config/analysislevelusage_7_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_7_recommended.globalconfig", "buildTransitive/config/analysislevelusage_7_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_all.globalconfig", "buildTransitive/config/analysislevelusage_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_default.globalconfig", "buildTransitive/config/analysislevelusage_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_minimum.globalconfig", "buildTransitive/config/analysislevelusage_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_none.globalconfig", "buildTransitive/config/analysislevelusage_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_8_recommended.globalconfig", "buildTransitive/config/analysislevelusage_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_all.globalconfig", "buildTransitive/config/analysislevelusage_9_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_default.globalconfig", "buildTransitive/config/analysislevelusage_9_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_minimum.globalconfig", "buildTransitive/config/analysislevelusage_9_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_none.globalconfig", "buildTransitive/config/analysislevelusage_9_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelusage_9_recommended.globalconfig", "buildTransitive/config/analysislevelusage_9_recommended_warnaserror.globalconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.NetAnalyzers.md", "documentation/Microsoft.CodeAnalysis.NetAnalyzers.sarif", "documentation/readme.md", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/DesignRulesDefault/.editorconfig", "editorconfig/DesignRulesEnabled/.editorconfig", "editorconfig/DocumentationRulesDefault/.editorconfig", "editorconfig/DocumentationRulesEnabled/.editorconfig", "editorconfig/GlobalizationRulesDefault/.editorconfig", "editorconfig/GlobalizationRulesEnabled/.editorconfig", "editorconfig/InteroperabilityRulesDefault/.editorconfig", "editorconfig/InteroperabilityRulesEnabled/.editorconfig", "editorconfig/MaintainabilityRulesDefault/.editorconfig", "editorconfig/MaintainabilityRulesEnabled/.editorconfig", "editorconfig/NamingRulesDefault/.editorconfig", "editorconfig/NamingRulesEnabled/.editorconfig", "editorconfig/PerformanceRulesDefault/.editorconfig", "editorconfig/PerformanceRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "editorconfig/ReliabilityRulesDefault/.editorconfig", "editorconfig/ReliabilityRulesEnabled/.editorconfig", "editorconfig/SecurityRulesDefault/.editorconfig", "editorconfig/SecurityRulesEnabled/.editorconfig", "editorconfig/UsageRulesDefault/.editorconfig", "editorconfig/UsageRulesEnabled/.editorconfig", "microsoft.codeanalysis.netanalyzers.9.0.0.nupkg.sha512", "microsoft.codeanalysis.netanalyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/DesignRulesDefault.ruleset", "rulesets/DesignRulesEnabled.ruleset", "rulesets/DocumentationRulesDefault.ruleset", "rulesets/DocumentationRulesEnabled.ruleset", "rulesets/GlobalizationRulesDefault.ruleset", "rulesets/GlobalizationRulesEnabled.ruleset", "rulesets/InteroperabilityRulesDefault.ruleset", "rulesets/InteroperabilityRulesEnabled.ruleset", "rulesets/MaintainabilityRulesDefault.ruleset", "rulesets/MaintainabilityRulesEnabled.ruleset", "rulesets/NamingRulesDefault.ruleset", "rulesets/NamingRulesEnabled.ruleset", "rulesets/PerformanceRulesDefault.ruleset", "rulesets/PerformanceRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "rulesets/ReliabilityRulesDefault.ruleset", "rulesets/ReliabilityRulesEnabled.ruleset", "rulesets/SecurityRulesDefault.ruleset", "rulesets/SecurityRulesEnabled.ruleset", "rulesets/UsageRulesDefault.ruleset", "rulesets/UsageRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}}, "projectFileDependencyGroups": {"net9.0": ["Microsoft.CodeAnalysis.NetAnalyzers >= 9.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Cadence.Domain.csproj", "projectName": "Cadence.Domain", "projectPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Cadence.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\Projects\\Cadence\\Source\\Cadence.Domain\\Build\\Extensions\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.CodeAnalysis.NetAnalyzers": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(,4.7.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,9.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,9.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,9.0.32767]", "System.Formats.Tar": "(,9.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,9.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,9.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,9.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,4.6.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,4.4.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,9.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,9.0.32767]", "System.Text.Json": "(,9.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,9.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,9.0.32767]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}}