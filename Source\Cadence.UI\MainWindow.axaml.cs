using System;
using Avalonia.Controls;
using Cadence.UI.Views;
using Cadence.UI.Views.ViewModels;
using Cadence.Rendering.Device;
using System.Diagnostics;
using System.IO;

using Avalonia.Threading;
using System.Threading.Tasks;
using Cadence.Rendering.Services;

namespace Cadence.UI;

internal sealed partial class MainWindow : Window, IDisposable
{
    private WebGpuContext? _webGpu;
    private IOffscreenRenderer? _renderer;

    private static void Log(string message)
    {
        try
        {
            var path1 = Path.Combine(AppContext.BaseDirectory, "ui-startup.log");
            File.AppendAllText(path1, message + Environment.NewLine);
        }
        catch (IOException) { }
        catch (UnauthorizedAccessException) { }
        catch (NotSupportedException) { }
        catch (System.Security.SecurityException) { }

        // Best-effort write to parent folder (net9.0) if RID subfolder is used
        try
        {
            var parent = Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, "..", "ui-startup.log"));
            File.AppendAllText(parent, message + Environment.NewLine);
        }
        catch (IOException) { }
        catch (UnauthorizedAccessException) { }
        catch (NotSupportedException) { }
        catch (System.Security.SecurityException) { }
    }

    public MainWindow()
    {
        InitializeComponent();
        WireDataContexts();
        this.Opened += OnOpened;
    }

#pragma warning disable CA1031

    private async Task InitializeGpuAsync()
    {
        try
        {
            // Preflight capability check to avoid calling into missing native runtime
            if (!WebGpuContext.IsRuntimeAvailable())
            {
                _renderer = new NullOffscreenRenderer();
                Debug.WriteLine("WebGPU runtime unavailable; using NullOffscreenRenderer");
                Log("Renderer: NullOffscreen (WebGPU runtime unavailable)");
                return;
            }

            await Task.Run(() =>
            {
                _webGpu = new WebGpuContext();
                _webGpu.Initialize();
                _renderer = new OffscreenRenderer(_webGpu);
            }).ConfigureAwait(false);
            Debug.WriteLine("Using OffscreenRenderer (WebGPU runtime available)");
            Log("Renderer: WebGPU Offscreen");
        }
        catch (InvalidOperationException ex)
        {
            Debug.WriteLine($"WebGPU init failed (invalid operation): {ex.Message}");
            _renderer = new NullOffscreenRenderer();
            _webGpu?.Dispose();
            _webGpu = null;
        }
        catch (NotSupportedException ex)
        {
            Debug.WriteLine($"WebGPU not supported: {ex.Message}");
            _renderer = new NullOffscreenRenderer();
            _webGpu?.Dispose();
            _webGpu = null;
        }
        catch (DllNotFoundException ex)
        {
            Debug.WriteLine($"WebGPU native library not found: {ex.Message}");
            _renderer = new NullOffscreenRenderer();
            _webGpu?.Dispose();
            _webGpu = null;
        }
        catch (EntryPointNotFoundException ex)
        {
            Debug.WriteLine($"WebGPU entrypoint missing: {ex.Message}");
            _renderer = new NullOffscreenRenderer();
            _webGpu?.Dispose();
            _webGpu = null;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Unexpected GPU init error: {ex}");
            _renderer = new NullOffscreenRenderer();
            _webGpu?.Dispose();
            _webGpu = null;
        }

    }
#pragma warning restore CA1031


    private void WireDataContexts()
    {
        if (this.Content is Grid grid)
        {
            // HeaderBarView at index 0 spans columns
            if (grid.Children.Count >= 1 && grid.Children[0] is HeaderBarView header)
            {
                var vm = new HeaderBarViewModel();
                header.DataContext = vm;
            }

            // SidePanelView at index 1
            if (grid.Children.Count >= 2 && grid.Children[1] is SidePanelView side)
            {
                var vm = new SidePanelViewModel();
                side.DataContext = vm;
            }

            // DateShelfView at index 2
            if (grid.Children.Count >= 3 && grid.Children[2] is DateShelfView dateShelf)
            {
                var vm = new DateShelfViewModel();
                dateShelf.DataContext = vm;
            }

            // TimelineView at index 3
            if (grid.Children.Count >= 4 && grid.Children[3] is TimelineView timeline)
            {
                var vm = new TimelineViewModel();
                timeline.DataContext = vm;
            }
        }
    }

    private async void OnOpened(object? sender, EventArgs e)
    {
#pragma warning disable CA1031

        try
        {
        Debug.WriteLine("Opened start");
        Log("Opened start");
        // Stay on UI thread; satisfy CA2007 by being explicit
        await InitializeGpuAsync().ConfigureAwait(true);

        if (_renderer is null) return;
        if (Content is not Grid grid) return;

        // Headless-friendly self-test of shader outputs (1x1) before UI updates
        var shaderIds = new[] { "Backgrounds/HeaderBar", "Backgrounds/SidePanel", "Backgrounds/DateShelf", "Backgrounds/Timeline" };
        foreach (var sid in shaderIds)
        {
            try
            {
                var px = await _renderer!.RenderBgra8Async(sid, 1, 1, System.Threading.CancellationToken.None).ConfigureAwait(false);
                Log($"SelfTest {sid} = {px[0]},{px[1]},{px[2]},{px[3]}");
            }
            catch (InvalidOperationException ex) { Log($"SelfTest {sid} failed: {ex.Message}"); }
            catch (NotSupportedException ex) { Log($"SelfTest {sid} not supported: {ex.Message}"); }
            catch (DllNotFoundException ex) { Log($"SelfTest {sid} dll missing: {ex.Message}"); }
            catch (EntryPointNotFoundException ex) { Log($"SelfTest {sid} entrypoint missing: {ex.Message}"); }
        }

        var tasks = new System.Collections.Generic.List<Task>();

        if (grid.Children.Count >= 1 && grid.Children[0] is HeaderBarView h && h.DataContext is HeaderBarViewModel hb)
            tasks.Add(RenderAndApplyAsync(hb.ShaderId, hb.SetBackgroundFromPixels));
        if (grid.Children.Count >= 2 && grid.Children[1] is SidePanelView s && s.DataContext is SidePanelViewModel sp)
            tasks.Add(RenderAndApplyAsync(sp.ShaderId, sp.SetBackgroundFromPixels));
        if (grid.Children.Count >= 3 && grid.Children[2] is DateShelfView d && d.DataContext is DateShelfViewModel ds)
            tasks.Add(RenderAndApplyAsync(ds.ShaderId, ds.SetBackgroundFromPixels));
        if (grid.Children.Count >= 4 && grid.Children[3] is TimelineView t && t.DataContext is TimelineViewModel tl)
            tasks.Add(RenderAndApplyAsync(tl.ShaderId, tl.SetBackgroundFromPixels));

        await Task.WhenAll(tasks).ConfigureAwait(false);
        Log($"Rendered backgrounds for {tasks.Count} panels");


        async Task RenderAndApplyAsync(string shaderId, Action<byte[], int, int> apply)
        {
            try
            {
                var pixels = await _renderer!.RenderBgra8Async(shaderId, 16, 16, System.Threading.CancellationToken.None).ConfigureAwait(false);
                await Dispatcher.UIThread.InvokeAsync(() => apply(pixels, 16, 16));
                Log($"Applied {shaderId} pixels len={pixels.Length}");
            }
            catch (InvalidOperationException ex)
            {
                Debug.WriteLine($"Render failed: {ex.Message}");
            }
            catch (NotSupportedException ex)
            {
                Debug.WriteLine($"Render not supported: {ex.Message}");
            }
            catch (DllNotFoundException ex)
            {
                Debug.WriteLine($"WebGPU DLL not found: {ex.Message}");
            }
            catch (EntryPointNotFoundException ex)
            {
                Debug.WriteLine($"WebGPU entrypoint not found: {ex.Message}");

            }
            catch (AccessViolationException ex)
            {
                Debug.WriteLine($"WebGPU access violation: {ex.Message}");

            }
            catch (IOException ex)
            {
                Debug.WriteLine($"WGSL I/O error: {ex.Message}");

            }
        }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Unhandled exception during startup: {ex}");
        }

#pragma warning restore CA1031

    }

    public void Dispose()
    {
        _renderer?.Dispose();
        _renderer = null;
        _webGpu?.Dispose();
        _webGpu = null;
    }
}