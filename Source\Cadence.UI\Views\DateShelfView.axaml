<UserControl xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:vm="clr-namespace:Cadence.UI.Views.ViewModels"
    x:Class="Cadence.UI.Views.DateShelfView"
    x:DataType="vm:DateShelfViewModel"
    MinHeight="36">
    <Border Background="{Binding Background}"
        BorderBrush="{DynamicResource ThemeBorderLowBrush}"
        BorderThickness="0,0,0,1"
        Padding="8,6">
        <ItemsControl ItemsSource="{Binding DateColumns}">
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal" Spacing="8" />
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <Border Padding="4,0">
                        <TextBlock Text="{Binding}" />
                    </Border>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </Border>
</UserControl>