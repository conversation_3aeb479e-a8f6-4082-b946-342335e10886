using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Avalonia.Media;
using Avalonia;
using Avalonia.Media.Imaging;

namespace Cadence.UI.Views.ViewModels;

internal sealed class HeaderBarViewModel : INotifyPropertyChanged, IDisposable
{
    private string _projectTitle = "Project";

    public string ShaderId { get; } = "Backgrounds/HeaderBar";

    private IBrush _background = Brushes.Transparent;
    public IBrush Background
    {
        get => _background;
        private set { if (_background == value) return; _background = value; OnPropertyChanged(); }
    }
    private WriteableBitmap? _backgroundBitmap;

    public void SetBackgroundFromPixels(byte[] bgraPixels, int width, int height)
    {
        _backgroundBitmap?.Dispose();
        var bmp = new WriteableBitmap(new PixelSize(width, height), new Vector(96,96),
            Avalonia.Platform.PixelFormat.Bgra8888, Avalonia.Platform.AlphaFormat.Unpremul);
        using (var fb = bmp.Lock())
        {
            System.Runtime.InteropServices.Marshal.Copy(bgraPixels, 0, fb.Address, bgraPixels.Length);
        }
        _backgroundBitmap = bmp;
        Background = new ImageBrush(_backgroundBitmap) { Stretch = Stretch.UniformToFill };
    }

    public void Dispose()
    {
        _backgroundBitmap?.Dispose();
        _backgroundBitmap = null;
    }

    public string ProjectTitle
    {
        get => _projectTitle;
        set
        {
            if (_projectTitle == value) return;
            _projectTitle = value;
            OnPropertyChanged();
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    private void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
}


