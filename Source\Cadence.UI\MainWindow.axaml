<Window xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:Cadence.UI.Views"
    mc:Ignorable="d" d:DesignWidth="1280" d:DesignHeight="800"
    x:Class="Cadence.UI.MainWindow"
    Title="Cadence"
    Width="1280"
    Height="800">
    <Grid RowDefinitions="Auto,Auto,*" ColumnDefinitions="Auto,*" Background="Black">
        <!-- Top-level project header bar (row 0, spans all columns) -->
        <views:HeaderBarView Grid.Row="0" Grid.ColumnSpan="2" />

        <!-- Sidepanel (row 1-2, column 0) -->
        <views:SidePanelView Grid.Row="1" Grid.RowSpan="2" Grid.Column="0" />

        <!-- Date shelf (row 1, column 1) -->
        <views:DateShelfView Grid.Row="1" Grid.Column="1" />

        <!-- Timeline panel (row 2, column 1) -->
        <views:TimelineView Grid.Row="2" Grid.Column="1" />
    </Grid>
</Window>