using System;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;

namespace Cadence.UI;

internal sealed partial class App : Avalonia.Application
{
    private static bool _handlersRegistered;

    private static void Log(string message)
    {
        var baseDir = AppContext.BaseDirectory;
        try
        {
            var ridPath = System.IO.Path.Combine(baseDir, "ui-crash.log");
            System.IO.File.AppendAllText(ridPath, message + Environment.NewLine);
        }
        catch (System.IO.IOException) { }
        catch (UnauthorizedAccessException) { }
        catch (NotSupportedException) { }
        catch (System.Security.SecurityException) { }

        try
        {
            var parent = System.IO.Path.GetFullPath(System.IO.Path.Combine(baseDir, "..", "ui-crash.log"));
            System.IO.File.AppendAllText(parent, message + Environment.NewLine);
        }
        catch (System.IO.IOException) { }
        catch (UnauthorizedAccessException) { }
        catch (NotSupportedException) { }
        catch (System.Security.SecurityException) { }
    }

    public override void Initialize()
    {
        // Global crash logging hooks - only register once
        if (!_handlersRegistered)
        {
            AppDomain.CurrentDomain.UnhandledException += (s, e) => Log($"UnhandledException: {e.ExceptionObject}");
            TaskScheduler.UnobservedTaskException += (s, e) => { Log($"UnobservedTaskException: {e.Exception}"); e.SetObserved(); };
            Dispatcher.UIThread.UnhandledException += (s, e) => { Log($"DispatcherUnhandledException: {e.Exception}"); /* do not set Handled to avoid masking crashes */ };
            _handlersRegistered = true;
        }

        Log("App.Initialize");
        try
        {
            AvaloniaXamlLoader.Load(this);
            Log("XAML loaded successfully");
        }
        catch (Exception ex)
        {
            Log($"XAML load failed: {ex}");
            throw;
        }
    }

    public override void OnFrameworkInitializationCompleted()
    {
        Log("OnFrameworkInitializationCompleted started");
        try
        {
            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                Log("Creating MainWindow");
                desktop.MainWindow = new MainWindow();
                Log("MainWindow created, showing window");
                desktop.MainWindow.Show();
                Log("MainWindow shown successfully");
            }
            else
            {
                Log($"ApplicationLifetime is not IClassicDesktopStyleApplicationLifetime: {ApplicationLifetime?.GetType().Name}");
            }
        }
        catch (Exception ex)
        {
            Log($"Startup failed creating/showing MainWindow: {ex}");
            throw;
        }

        Log("Calling base.OnFrameworkInitializationCompleted()");
        base.OnFrameworkInitializationCompleted();
        Log("OnFrameworkInitializationCompleted completed");
    }
}