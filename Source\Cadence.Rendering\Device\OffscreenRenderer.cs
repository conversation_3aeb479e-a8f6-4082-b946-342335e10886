using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using Silk.NET.Core.Native;
using Silk.NET.WebGPU;

namespace Cadence.Rendering.Device;

public sealed class OffscreenRenderer : IOffscreenRenderer, IDisposable
{
    private static readonly WebGPU _wgpu = WebGPU.GetApi();
    private readonly WebGpuContext _context;
    private TextureFormat _format = TextureFormat.Bgra8Unorm;
    private unsafe RenderPipeline* _pipeline;
    private readonly object _pipelineLock = new();
    private readonly object _renderLock = new();



    public OffscreenRenderer(WebGpuContext context)
    {
        _context = context;
    }

    public unsafe void CompileFlatColorPipeline(string shaderId)
    {
        var wgsl = ShaderCompiler.LoadWgslFromContent(shaderId);
        var module = ShaderCompiler.CreateShaderModuleFromWgsl(_wgpu, _context.Device, wgsl);

        // Vertex state
        VertexState vertex = new()
        {
            Module = module,
            EntryPoint = (byte*)SilkMarshal.StringToPtr("vs_main")
        };

        // Fragment state
        ColorTargetState colorTarget = new()
        {
            Format = _format,
            Blend = null,
            WriteMask = ColorWriteMask.All
        };
        FragmentState fragment = new()
        {
            Module = module,
            EntryPoint = (byte*)SilkMarshal.StringToPtr("fs_main"),
            TargetCount = 1,
        };
        var targets = stackalloc ColorTargetState[1];
        targets[0] = colorTarget;
        fragment.Targets = targets;

        // Pipeline layout (explicit empty layout to avoid backend-specific auto-layout issues)
        PipelineLayoutDescriptor layoutDesc = new()
        {
            BindGroupLayoutCount = 0,
            BindGroupLayouts = null
        };
        var pipelineLayout = _wgpu.DeviceCreatePipelineLayout(_context.Device, &layoutDesc);

        // Pipeline
        RenderPipelineDescriptor desc = new()
        {
            Layout = pipelineLayout,
            Vertex = vertex,
            Fragment = &fragment,
            Primitive = new PrimitiveState { Topology = PrimitiveTopology.TriangleList }
        };

        _pipeline = _wgpu.DeviceCreateRenderPipeline(_context.Device, &desc);
        if (_pipeline is null)
        {
            SilkMarshal.Free((nint)vertex.EntryPoint);
            SilkMarshal.Free((nint)fragment.EntryPoint);
            _wgpu.PipelineLayoutRelease(pipelineLayout);
            _wgpu.ShaderModuleRelease(module);
            throw new InvalidOperationException("Failed to create render pipeline.");
        }

        // Free allocated strings and layout
        SilkMarshal.Free((nint)vertex.EntryPoint);
        SilkMarshal.Free((nint)fragment.EntryPoint);
        _wgpu.PipelineLayoutRelease(pipelineLayout);
        _wgpu.ShaderModuleRelease(module);
    }

    #pragma warning disable CA1001
    private sealed class MapWaiter
    {
        public System.Threading.ManualResetEventSlim Evt = new(false);
        public BufferMapAsyncStatus Status;
    }
    #pragma warning restore CA1001

    [UnmanagedCallersOnly(CallConvs = new[] { typeof(System.Runtime.CompilerServices.CallConvCdecl) })]
    private static unsafe void BufferMapCallback(BufferMapAsyncStatus status, void* userData)
    {
        var handle = GCHandle.FromIntPtr((nint)userData);
        var waiter = (MapWaiter)handle.Target!;
        waiter.Status = status;
        waiter.Evt.Set();
    }

    private unsafe byte[] RenderBgra8Core(string shaderId, int width, int height)
    {
        if (_pipeline == null)
        {
            lock (_pipelineLock)
            {
                if (_pipeline == null)
                {
                    CompileFlatColorPipeline(shaderId);
                }
            }
        }

        TextureView* view = null;
        Texture* texture = null;
        Silk.NET.WebGPU.Buffer* buffer = null;
        CommandBuffer* command = null;

        try
        {
            // Create color texture
            TextureDescriptor texDesc = new()
            {
                Size = new Extent3D((uint)width, (uint)height, 1),
                MipLevelCount = 1,
                SampleCount = 1,
                Dimension = TextureDimension.Dimension2D,
                Format = _format,
                Usage = TextureUsage.RenderAttachment | TextureUsage.CopySrc
            };

            texture = _wgpu.DeviceCreateTexture(_context.Device, &texDesc);
            view = _wgpu.TextureCreateView(texture, null);

            // Render pass
            RenderPassColorAttachment colorAttachment = new()
            {
                View = view,
                ResolveTarget = null,
                LoadOp = LoadOp.Clear,
                StoreOp = StoreOp.Store,
                ClearValue = new Color(0, 0, 0, 0)
            };

            var attachments = stackalloc RenderPassColorAttachment[1];
            attachments[0] = colorAttachment;
            RenderPassDescriptor passDesc = new()
            {
                ColorAttachmentCount = 1,
                ColorAttachments = attachments
            };

            CommandEncoderDescriptor encDesc = new();
            var encoder = _wgpu.DeviceCreateCommandEncoder(_context.Device, &encDesc);
            var pass = _wgpu.CommandEncoderBeginRenderPass(encoder, &passDesc);
            _wgpu.RenderPassEncoderSetPipeline(pass, _pipeline);
            // Set viewport and scissor explicitly
            _wgpu.RenderPassEncoderSetViewport(pass, 0, 0, (float)width, (float)height, 0, 1);
            _wgpu.RenderPassEncoderSetScissorRect(pass, 0, 0, (uint)width, (uint)height);
            _wgpu.RenderPassEncoderDraw(pass, 6, 1, 0, 0);
            _wgpu.RenderPassEncoderEnd(pass);
            _wgpu.RenderPassEncoderRelease(pass);

            // Create readback buffer
            int bytesPerPixel = 4;
            int unpaddedBpr = width * bytesPerPixel;
            int paddedBpr = ((unpaddedBpr + 255) / 256) * 256;
            nuint bufferSize = (nuint)(paddedBpr * height);

            BufferDescriptor bufDesc = new()
            {
                Usage = BufferUsage.MapRead | BufferUsage.CopyDst,
                Size = bufferSize
            };
            buffer = _wgpu.DeviceCreateBuffer(_context.Device, &bufDesc);

            // Copy texture to buffer
            ImageCopyTexture src = new()
            {
                Texture = texture,
                MipLevel = 0,
                Origin = new Origin3D(0, 0, 0),
                Aspect = TextureAspect.All
            };
            TextureDataLayout layout = new()
            {
                Offset = 0,
                BytesPerRow = (uint)paddedBpr,
                RowsPerImage = (uint)height
            };
            ImageCopyBuffer dst = new()
            {
                Buffer = buffer,
                Layout = layout
            };
            Extent3D copySize = new((uint)width, (uint)height, 1);
            _wgpu.CommandEncoderCopyTextureToBuffer(encoder, &src, &dst, &copySize);

            // Submit
            command = _wgpu.CommandEncoderFinish(encoder, null);
            _wgpu.CommandEncoderRelease(encoder);
            _wgpu.QueueSubmit(_context.Queue, 1, &command);

            // Map and readback (blocking inside background thread)
            var waiter = new MapWaiter();
            var handle = GCHandle.Alloc(waiter);
            try
            {
                delegate* unmanaged[Cdecl]<BufferMapAsyncStatus, void*, void> cbPtr = &BufferMapCallback;
                _wgpu.BufferMapAsync(buffer, MapMode.Read, 0, bufferSize, new PfnBufferMapCallback(cbPtr), (void*)GCHandle.ToIntPtr(handle));
                waiter.Evt.Wait();
            }
            finally
            {
                handle.Free();
            }

            if (waiter.Status != BufferMapAsyncStatus.Success)
                throw new InvalidOperationException($"BufferMapAsync failed: {waiter.Status}");

            var data = (byte*)_wgpu.BufferGetMappedRange(buffer, 0, bufferSize);
            if (data == null)
                throw new InvalidOperationException("BufferGetMappedRange returned null");
            var pixels = new byte[width * height * bytesPerPixel];
            for (int y = 0; y < height; y++)
            {
                var srcRow = data + y * paddedBpr;
                var dstIndex = y * unpaddedBpr;
                Marshal.Copy(new IntPtr(srcRow), pixels, dstIndex, unpaddedBpr);
            }
            _wgpu.BufferUnmap(buffer);

            return pixels;
        }
        finally
        {
            if (view != null) _wgpu.TextureViewRelease(view);
            if (texture != null) _wgpu.TextureRelease(texture);
            if (buffer != null) _wgpu.BufferRelease(buffer);
            if (command != null) _wgpu.CommandBufferRelease(command);
        }
    }

    public Task<byte[]> RenderBgra8Async(string shaderId, int width, int height, CancellationToken cancellationToken)
    {
        return Task.Run(() =>
        {
            lock (_renderLock)
            {
                return RenderBgra8Core(shaderId, width, height);
            }
        }, cancellationToken);
    }

    public unsafe void Dispose()
    {
        if (_pipeline != null)
        {
            _wgpu.RenderPipelineRelease(_pipeline);
            _pipeline = null;
        }
        // Do not dispose the global WebGPU API wrapper here; it's shared.
    }
}


